<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .qr-container {
            text-align: center;
            margin: 15px 0;
            min-height: 220px;
            border: 1px dashed #ccc;
            padding: 10px;
            border-radius: 8px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>二维码生成调试</h1>
        
        <div class="test-section">
            <h3>库加载状态检测</h3>
            <div id="libraryStatus"></div>
            <button onclick="checkLibraryStatus()">重新检测</button>
        </div>

        <div class="test-section">
            <h3>方法1: 使用qrcode.js库</h3>
            <div class="qr-container" id="qr1"></div>
            <button onclick="testMethod1()">测试方法1</button>
        </div>

        <div class="test-section">
            <h3>方法2: 使用在线API (QR Server)</h3>
            <div class="qr-container" id="qr2"></div>
            <button onclick="testMethod2()">测试方法2</button>
        </div>

        <div class="test-section">
            <h3>方法3: 使用Google Charts API</h3>
            <div class="qr-container" id="qr3"></div>
            <button onclick="testMethod3()">测试方法3</button>
        </div>

        <div class="test-section">
            <h3>调试日志</h3>
            <div class="log" id="debugLog"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 二维码库 -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js" onerror="loadBackupQRCode()"></script>
    <script>
        // 测试用的支付链接
        const testQRData = 'https://qr.alipay.com/tsx12473ducxlzawpdayn0b';
        
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        // 备用库加载
        function loadBackupQRCode() {
            log('主要二维码库加载失败，尝试备用CDN');
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/qrcode@1.5.3/build/qrcode.min.js';
            script.onload = function() {
                log('备用二维码库加载成功');
                checkLibraryStatus();
            };
            script.onerror = function() {
                log('备用二维码库也加载失败');
                window.QRCodeLoadFailed = true;
                checkLibraryStatus();
            };
            document.head.appendChild(script);
        }

        // 检测库状态
        function checkLibraryStatus() {
            const statusDiv = document.getElementById('libraryStatus');
            const qrCodeAvailable = typeof QRCode !== 'undefined';
            const loadFailed = window.QRCodeLoadFailed;
            
            let status = '';
            if (qrCodeAvailable && !loadFailed) {
                status = '<span style="color: green;">✓ QRCode库可用</span>';
            } else if (loadFailed) {
                status = '<span style="color: red;">✗ QRCode库加载失败</span>';
            } else {
                status = '<span style="color: orange;">? QRCode库状态未知</span>';
            }
            
            statusDiv.innerHTML = status;
            log(`库状态检测: QRCode=${typeof QRCode}, LoadFailed=${loadFailed}`);
        }

        // 方法1: 使用qrcode.js库
        function testMethod1() {
            const container = document.getElementById('qr1');
            container.innerHTML = '';
            log('开始测试方法1: qrcode.js库');

            if (typeof QRCode !== 'undefined' && !window.QRCodeLoadFailed) {
                try {
                    const canvas = document.createElement('canvas');
                    canvas.style.border = '1px solid #ddd';
                    container.appendChild(canvas);
                    
                    QRCode.toCanvas(canvas, testQRData, {
                        width: 200,
                        height: 200,
                        margin: 2,
                        color: {
                            dark: "#000000",
                            light: "#ffffff"
                        }
                    }, function (error) {
                        if (error) {
                            log('方法1失败: ' + error.message);
                            container.innerHTML = '<p style="color: red;">生成失败: ' + error.message + '</p>';
                        } else {
                            log('方法1成功: 二维码生成完成');
                        }
                    });
                } catch (e) {
                    log('方法1异常: ' + e.message);
                    container.innerHTML = '<p style="color: red;">生成异常: ' + e.message + '</p>';
                }
            } else {
                log('方法1跳过: QRCode库不可用');
                container.innerHTML = '<p style="color: orange;">QRCode库不可用</p>';
            }
        }

        // 方法2: 使用在线API
        function testMethod2() {
            const container = document.getElementById('qr2');
            container.innerHTML = '';
            log('开始测试方法2: QR Server API');

            const img = document.createElement('img');
            img.style.cssText = 'width: 200px; height: 200px; border: 1px solid #ddd;';
            img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(testQRData);
            
            img.onload = function() {
                log('方法2成功: QR Server API生成完成');
            };
            
            img.onerror = function() {
                log('方法2失败: QR Server API无法访问');
                container.innerHTML = '<p style="color: red;">QR Server API失败</p>';
            };
            
            container.appendChild(img);
        }

        // 方法3: 使用Google Charts API
        function testMethod3() {
            const container = document.getElementById('qr3');
            container.innerHTML = '';
            log('开始测试方法3: Google Charts API');

            const img = document.createElement('img');
            img.style.cssText = 'width: 200px; height: 200px; border: 1px solid #ddd;';
            img.src = 'https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=' + encodeURIComponent(testQRData);
            
            img.onload = function() {
                log('方法3成功: Google Charts API生成完成');
            };
            
            img.onerror = function() {
                log('方法3失败: Google Charts API无法访问');
                container.innerHTML = '<p style="color: red;">Google Charts API失败</p>';
            };
            
            container.appendChild(img);
        }

        // 页面加载完成后自动检测
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始检测');
            setTimeout(checkLibraryStatus, 1000);
        });
    </script>
</body>
</html>
