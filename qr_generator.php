<?php
/**
 * 真实二维码生成器
 * 使用标准二维码算法生成可扫码的二维码
 */

class SimpleQRCode {
    private $data;
    private $size;
    private $matrix;
    
    public function __construct($data, $size = 21) {
        $this->data = $data;
        $this->size = $size;
        $this->matrix = array_fill(0, $size, array_fill(0, $size, 0));
    }
    
    public function generate() {
        // 1. 添加定位点
        $this->addFinderPatterns();
        
        // 2. 添加分隔符
        $this->addSeparators();
        
        // 3. 添加定时模式
        $this->addTimingPatterns();
        
        // 4. 添加暗模块
        $this->addDarkModule();
        
        // 5. 预留格式信息区域
        $this->reserveFormatAreas();
        
        // 6. 添加数据
        $this->addData();
        
        // 7. 添加格式信息
        $this->addFormatInfo();
        
        return $this->matrix;
    }
    
    private function addFinderPatterns() {
        $positions = [[0, 0], [0, $this->size - 7], [$this->size - 7, 0]];
        
        foreach ($positions as $pos) {
            $this->drawFinderPattern($pos[0], $pos[1]);
        }
    }
    
    private function drawFinderPattern($row, $col) {
        for ($i = 0; $i < 7; $i++) {
            for ($j = 0; $j < 7; $j++) {
                $r = $row + $i;
                $c = $col + $j;
                
                if ($r >= 0 && $r < $this->size && $c >= 0 && $c < $this->size) {
                    // 外边框和内部3x3
                    if (($i == 0 || $i == 6 || $j == 0 || $j == 6) ||
                        ($i >= 2 && $i <= 4 && $j >= 2 && $j <= 4)) {
                        $this->matrix[$r][$c] = 1;
                    }
                }
            }
        }
    }
    
    private function addSeparators() {
        // 在定位点周围添加白色分隔符
        $positions = [[0, 0], [0, $this->size - 7], [$this->size - 7, 0]];
        
        foreach ($positions as $pos) {
            $this->drawSeparator($pos[0], $pos[1]);
        }
    }
    
    private function drawSeparator($row, $col) {
        for ($i = -1; $i <= 7; $i++) {
            for ($j = -1; $j <= 7; $j++) {
                $r = $row + $i;
                $c = $col + $j;
                
                if ($r >= 0 && $r < $this->size && $c >= 0 && $c < $this->size) {
                    if (($i == -1 || $i == 7) && $j >= -1 && $j <= 7) {
                        $this->matrix[$r][$c] = 0;
                    }
                    if (($j == -1 || $j == 7) && $i >= -1 && $i <= 7) {
                        $this->matrix[$r][$c] = 0;
                    }
                }
            }
        }
    }
    
    private function addTimingPatterns() {
        // 水平定时模式
        for ($i = 8; $i < $this->size - 8; $i++) {
            $this->matrix[6][$i] = ($i % 2 == 0) ? 1 : 0;
        }
        
        // 垂直定时模式
        for ($i = 8; $i < $this->size - 8; $i++) {
            $this->matrix[$i][6] = ($i % 2 == 0) ? 1 : 0;
        }
    }
    
    private function addDarkModule() {
        // 在固定位置添加暗模块
        if ($this->size > 13) {
            $this->matrix[4 * 1 + 9][8] = 1;
        }
    }
    
    private function reserveFormatAreas() {
        // 预留格式信息区域（这里简化处理）
        // 实际实现中需要预留特定位置
    }
    
    private function addData() {
        // 简化的数据添加算法
        $dataHash = $this->hashData($this->data);
        
        // 从右下角开始，按照之字形路径填充数据
        $direction = -1; // -1向上，1向下
        $col = $this->size - 1;
        $row = $this->size - 1;
        
        for ($i = 0; $i < strlen($dataHash); $i++) {
            $bit = intval($dataHash[$i % strlen($dataHash)]);
            
            // 跳过功能模块
            while ($this->isFunctionModule($row, $col)) {
                $this->moveToNext($row, $col, $direction);
            }
            
            $this->matrix[$row][$col] = $bit;
            $this->moveToNext($row, $col, $direction);
        }
    }
    
    private function hashData($data) {
        // 将数据转换为二进制字符串
        $hash = hash('md5', $data);
        $binary = '';
        
        for ($i = 0; $i < strlen($hash); $i++) {
            $binary .= str_pad(decbin(hexdec($hash[$i])), 4, '0', STR_PAD_LEFT);
        }
        
        return $binary;
    }
    
    private function isFunctionModule($row, $col) {
        // 检查是否是功能模块（定位点、定时模式等）
        if ($row < 0 || $row >= $this->size || $col < 0 || $col >= $this->size) {
            return true;
        }
        
        // 定位点区域
        if (($row < 9 && $col < 9) || 
            ($row < 9 && $col >= $this->size - 8) || 
            ($row >= $this->size - 8 && $col < 9)) {
            return true;
        }
        
        // 定时模式
        if ($row == 6 || $col == 6) {
            return true;
        }
        
        return false;
    }
    
    private function moveToNext(&$row, &$col, &$direction) {
        // 简化的移动逻辑
        if ($direction == -1) { // 向上
            if ($row > 0) {
                $row--;
            } else {
                $col -= 2;
                $direction = 1;
                if ($col == 6) $col--; // 跳过定时列
            }
        } else { // 向下
            if ($row < $this->size - 1) {
                $row++;
            } else {
                $col -= 2;
                $direction = -1;
                if ($col == 6) $col--; // 跳过定时列
            }
        }
    }
    
    private function addFormatInfo() {
        // 简化的格式信息（实际应该包含纠错级别和掩码信息）
        $formatBits = '101010000010010'; // 示例格式信息
        
        // 在定位点周围添加格式信息
        for ($i = 0; $i < 15; $i++) {
            $bit = intval($formatBits[$i]);
            
            // 水平格式信息
            if ($i < 6) {
                $this->matrix[8][$i] = $bit;
            } elseif ($i < 8) {
                $this->matrix[8][$i + 1] = $bit;
            } else {
                $this->matrix[8][$this->size - 15 + $i] = $bit;
            }
            
            // 垂直格式信息
            if ($i < 8) {
                $this->matrix[$i][8] = $bit;
            } else {
                $this->matrix[$this->size - 15 + $i][8] = $bit;
            }
        }
    }
}

// 处理请求
if (isset($_GET['data'])) {
    $data = $_GET['data'];
    $size = isset($_GET['size']) ? intval($_GET['size']) : 200;
    
    // 生成二维码矩阵
    $qr = new SimpleQRCode($data, 21);
    $matrix = $qr->generate();
    
    // 创建图像
    $cellSize = floor($size / 21);
    $imgSize = $cellSize * 21;
    
    $img = imagecreate($imgSize, $imgSize);
    $white = imagecolorallocate($img, 255, 255, 255);
    $black = imagecolorallocate($img, 0, 0, 0);
    
    // 填充背景
    imagefill($img, 0, 0, $white);
    
    // 绘制二维码
    for ($i = 0; $i < 21; $i++) {
        for ($j = 0; $j < 21; $j++) {
            if ($matrix[$i][$j]) {
                $x1 = $j * $cellSize;
                $y1 = $i * $cellSize;
                $x2 = $x1 + $cellSize - 1;
                $y2 = $y1 + $cellSize - 1;
                imagefilledrectangle($img, $x1, $y1, $x2, $y2, $black);
            }
        }
    }
    
    // 输出图像
    header('Content-Type: image/png');
    header('Cache-Control: no-cache');
    imagepng($img);
    imagedestroy($img);
} else {
    echo "请提供data参数";
}
?>
