<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>支付API测试工具</h1>
        
        <div class="section">
            <h3>请求参数设置</h3>
            <div class="form-group">
                <label>套餐类型:</label>
                <select id="package">
                    <option value="day">日卡</option>
                    <option value="week">周卡</option>
                    <option value="month">月卡</option>
                    <option value="year">年卡</option>
                </select>
            </div>
            <div class="form-group">
                <label>价格:</label>
                <input type="number" id="price" value="0.01" step="0.01">
            </div>
            <div class="form-group">
                <label>支付方式:</label>
                <select id="payType">
                    <option value="alipay">支付宝</option>
                    <option value="wxpay">微信支付</option>
                </select>
            </div>
            <button onclick="testPaymentAPI()">发送支付请求</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="section">
            <h3>请求详情</h3>
            <div id="requestInfo" class="log">等待发送请求...</div>
        </div>

        <div class="section">
            <h3>响应详情</h3>
            <div id="responseInfo" class="log">等待接收响应...</div>
        </div>

        <div class="section">
            <h3>二维码/支付链接</h3>
            <div id="paymentResult" style="text-align: center; min-height: 200px; border: 1px dashed #ccc; padding: 20px;">
                等待支付结果...
            </div>
        </div>
    </div>

    <script>
        function log(message, elementId = 'responseInfo') {
            const element = document.getElementById(elementId);
            const time = new Date().toLocaleTimeString();
            element.textContent += `[${time}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog() {
            document.getElementById('requestInfo').textContent = '等待发送请求...';
            document.getElementById('responseInfo').textContent = '等待接收响应...';
            document.getElementById('paymentResult').innerHTML = '等待支付结果...';
        }

        function testPaymentAPI() {
            const packageType = document.getElementById('package').value;
            const price = document.getElementById('price').value;
            const payType = document.getElementById('payType').value;

            // 清空之前的结果
            document.getElementById('responseInfo').textContent = '';
            document.getElementById('paymentResult').innerHTML = '正在处理...';

            // 构建请求参数
            const postData = `package=${encodeURIComponent(packageType)}&price=${encodeURIComponent(price)}&pay_type=${encodeURIComponent(payType)}`;

            // 显示请求信息
            const requestInfo = `请求URL: process_payment.php
请求方法: POST
请求参数:
  - package: ${packageType}
  - price: ${price}
  - pay_type: ${payType}
  
POST数据: ${postData}`;

            document.getElementById('requestInfo').textContent = requestInfo;

            log('开始发送支付请求...');
            log(`套餐: ${packageType}, 价格: ${price}, 支付方式: ${payType}`);

            // 发送AJAX请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'process_payment.php', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            xhr.onreadystatechange = function() {
                log(`readyState: ${xhr.readyState}, status: ${xhr.status}`);

                if (xhr.readyState === 4) {
                    log(`最终状态: ${xhr.status}`);
                    log(`响应头: ${xhr.getAllResponseHeaders()}`);
                    log(`原始响应: ${xhr.responseText}`);

                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            
                            log('=== 解析后的响应对象 ===');
                            log(JSON.stringify(response, null, 2));
                            
                            // 显示响应字段详情
                            log('=== 响应字段详情 ===');
                            Object.keys(response).forEach(key => {
                                log(`${key}: ${response[key]}`);
                            });

                            // 处理支付结果
                            const resultDiv = document.getElementById('paymentResult');
                            
                            if (response.success) {
                                log('支付订单创建成功！');
                                
                                let resultHTML = `
                                    <div style="color: green; font-weight: bold; margin-bottom: 15px;">
                                        ✓ 支付订单创建成功
                                    </div>
                                    <div style="margin-bottom: 10px;">
                                        <strong>订单号:</strong> ${response.trade_no || '未知'}
                                    </div>
                                    <div style="margin-bottom: 10px;">
                                        <strong>金额:</strong> ¥${response.money || price}
                                    </div>
                                    <div style="margin-bottom: 10px;">
                                        <strong>支付方式:</strong> ${response.pay_type || payType}
                                    </div>
                                    <div style="margin-bottom: 15px;">
                                        <strong>支付方法:</strong> ${response.pay_method || '未知'}
                                    </div>
                                `;

                                // 显示二维码或支付链接
                                if (response.qrcode) {
                                    log(`获得支付链接: ${response.qrcode}`);
                                    
                                    if (response.pay_method === 'qrcode') {
                                        // 尝试显示二维码
                                        resultHTML += `
                                            <div style="margin-bottom: 15px;">
                                                <strong>二维码:</strong><br>
                                                <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(response.qrcode)}" 
                                                     style="border: 1px solid #ddd; border-radius: 8px;"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                                <div style="display: none; color: red;">二维码生成失败</div>
                                            </div>
                                        `;
                                    }
                                    
                                    resultHTML += `
                                        <div>
                                            <a href="${response.qrcode}" target="_blank" 
                                               style="display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 6px;">
                                                立即支付
                                            </a>
                                        </div>
                                        <div style="margin-top: 10px; font-size: 12px; color: #666;">
                                            支付链接: ${response.qrcode}
                                        </div>
                                    `;
                                } else {
                                    log('警告: 响应中没有支付链接');
                                    resultHTML += `<div style="color: orange;">警告: 没有获得支付链接</div>`;
                                }

                                resultDiv.innerHTML = resultHTML;
                            } else {
                                log(`支付失败: ${response.message}`);
                                resultDiv.innerHTML = `
                                    <div style="color: red; font-weight: bold;">
                                        ✗ 支付失败: ${response.message}
                                    </div>
                                `;
                            }

                        } catch (e) {
                            log(`JSON解析失败: ${e.message}`);
                            document.getElementById('paymentResult').innerHTML = `
                                <div style="color: red;">
                                    JSON解析失败: ${e.message}
                                </div>
                            `;
                        }
                    } else {
                        log(`HTTP错误: ${xhr.status}`);
                        document.getElementById('paymentResult').innerHTML = `
                            <div style="color: red;">
                                HTTP错误: ${xhr.status}
                            </div>
                        `;
                    }
                }
            };

            xhr.onerror = function() {
                log('网络错误');
                document.getElementById('paymentResult').innerHTML = `
                    <div style="color: red;">网络错误</div>
                `;
            };

            xhr.send(postData);
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，可以开始测试支付API', 'requestInfo');
        });
    </script>
</body>
</html>
