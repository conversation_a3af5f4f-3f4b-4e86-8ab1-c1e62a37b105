<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单二维码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .qr-container {
            margin: 15px 0;
            min-height: 220px;
            border: 1px dashed #ccc;
            padding: 10px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .qr-container img {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简单二维码测试</h1>
        <p>这是最简化的二维码生成方案测试</p>
        
        <div class="test-section">
            <h3>QR Server API 测试</h3>
            <div class="qr-container" id="qr1"></div>
            <button onclick="testQRServer()">测试 QR Server</button>
            <div id="status1"></div>
        </div>

        <div class="test-section">
            <h3>Google Charts API 测试</h3>
            <div class="qr-container" id="qr2"></div>
            <button onclick="testGoogleCharts()">测试 Google Charts</button>
            <div id="status2"></div>
        </div>

        <div class="test-section">
            <h3>支付链接测试</h3>
            <div class="qr-container" id="qr3"></div>
            <button onclick="testPaymentLink()">测试支付链接</button>
            <div id="status3"></div>
        </div>

        <div class="test-section">
            <h3>说明</h3>
            <p>测试链接: <strong>https://qr.alipay.com/tsx12473ducxlzawpdayn0b</strong></p>
            <p>如果二维码能正常显示并可以扫码，说明方案可行。</p>
            <p>如果二维码无法显示，会自动显示支付链接。</p>
        </div>
    </div>

    <script>
        const testData = 'https://qr.alipay.com/tsx12473ducxlzawpdayn0b';
        
        function testQRServer() {
            const container = document.getElementById('qr1');
            const status = document.getElementById('status1');
            
            container.innerHTML = '';
            status.innerHTML = '正在测试...';
            
            const img = document.createElement('img');
            img.style.cssText = `
                width: 200px;
                height: 200px;
                border: 1px solid #ddd;
                border-radius: 8px;
                background: white;
            `;
            
            img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(testData);
            
            img.onload = function() {
                status.innerHTML = '<span class="success">✓ QR Server API 可用</span>';
                console.log('QR Server API 测试成功');
            };
            
            img.onerror = function() {
                status.innerHTML = '<span class="error">✗ QR Server API 不可用</span>';
                container.innerHTML = '<p style="color: red;">无法加载二维码</p>';
                console.error('QR Server API 测试失败');
            };
            
            container.appendChild(img);
        }

        function testGoogleCharts() {
            const container = document.getElementById('qr2');
            const status = document.getElementById('status2');
            
            container.innerHTML = '';
            status.innerHTML = '正在测试...';
            
            const img = document.createElement('img');
            img.style.cssText = `
                width: 200px;
                height: 200px;
                border: 1px solid #ddd;
                border-radius: 8px;
                background: white;
            `;
            
            img.src = 'https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=' + encodeURIComponent(testData);
            
            img.onload = function() {
                status.innerHTML = '<span class="success">✓ Google Charts API 可用</span>';
                console.log('Google Charts API 测试成功');
            };
            
            img.onerror = function() {
                status.innerHTML = '<span class="error">✗ Google Charts API 不可用</span>';
                container.innerHTML = '<p style="color: red;">无法加载二维码</p>';
                console.error('Google Charts API 测试失败');
            };
            
            container.appendChild(img);
        }

        function testPaymentLink() {
            const container = document.getElementById('qr3');
            const status = document.getElementById('status3');
            
            container.innerHTML = '';
            status.innerHTML = '生成支付链接...';
            
            // 模拟二维码加载失败，直接显示支付链接
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                text-align: center;
                padding: 20px;
                border: 2px dashed #ddd;
                border-radius: 8px;
                background: #f9f9f9;
                margin-bottom: 15px;
            `;
            messageDiv.innerHTML = `
                <div style="font-size: 48px; margin-bottom: 10px;">📱</div>
                <div style="color: #666; margin-bottom: 15px;">二维码无法加载</div>
            `;
            
            const link = document.createElement('a');
            link.href = testData;
            link.textContent = '点击此处完成支付';
            link.target = '_blank';
            link.style.cssText = `
                display: inline-block;
                padding: 12px 24px;
                background: #5757ff;
                color: white;
                text-decoration: none;
                border-radius: 6px;
                font-weight: bold;
                transition: background 0.3s;
            `;
            link.addEventListener('mouseover', function() {
                this.style.background = '#4a4aff';
            });
            link.addEventListener('mouseout', function() {
                this.style.background = '#5757ff';
            });
            
            messageDiv.appendChild(link);
            container.appendChild(messageDiv);
            
            status.innerHTML = '<span class="success">✓ 支付链接生成成功</span>';
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始自动测试');
            
            setTimeout(function() {
                testQRServer();
            }, 500);
            
            setTimeout(function() {
                testGoogleCharts();
            }, 1000);
        });
    </script>
</body>
</html>
