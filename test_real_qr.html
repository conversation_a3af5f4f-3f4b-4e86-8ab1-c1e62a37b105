<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实二维码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .qr-container {
            margin: 15px 0;
            min-height: 220px;
            border: 1px dashed #ccc;
            padding: 10px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .qr-container img {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .info {
            color: #666;
            font-size: 12px;
            margin-top: 10px;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>真实二维码生成测试</h1>
        
        <div class="test-section">
            <h3>自定义二维码测试</h3>
            <div>
                <input type="text" id="customData" placeholder="输入要生成二维码的内容" value="https://qr.alipay.com/tsx12473ducxlzawpdayn0b">
                <button onclick="generateCustomQR()">生成二维码</button>
            </div>
            <div class="qr-container" id="custom-qr"></div>
            <div id="custom-status"></div>
        </div>

        <div class="test-section">
            <h3>本地PHP生成器测试</h3>
            <div class="qr-container" id="php-qr"></div>
            <button onclick="testPHPGenerator()">测试PHP生成器</button>
            <div id="php-status"></div>
        </div>

        <div class="test-section">
            <h3>在线服务对比测试</h3>
            <div style="display: flex; justify-content: space-around; flex-wrap: wrap;">
                <div style="margin: 10px;">
                    <h4>QR Server API</h4>
                    <div class="qr-container" id="qr-server" style="min-height: 150px;"></div>
                    <button onclick="testQRServer()">测试</button>
                </div>
                <div style="margin: 10px;">
                    <h4>Google Charts</h4>
                    <div class="qr-container" id="google-charts" style="min-height: 150px;"></div>
                    <button onclick="testGoogleCharts()">测试</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>扫码测试说明</h3>
            <p>请使用手机扫码软件扫描上方生成的二维码，验证是否能正确识别链接内容。</p>
            <p><strong>测试链接：</strong> <span id="test-url">https://qr.alipay.com/tsx12473ducxlzawpdayn0b</span></p>
            <div class="info">
                如果二维码可以被正确扫描并跳转到支付宝，说明生成的二维码是有效的。
            </div>
        </div>
    </div>

    <script>
        const defaultTestData = 'https://qr.alipay.com/tsx12473ducxlzawpdayn0b';
        
        function generateCustomQR() {
            const data = document.getElementById('customData').value || defaultTestData;
            const container = document.getElementById('custom-qr');
            const status = document.getElementById('custom-status');
            
            container.innerHTML = '';
            status.innerHTML = '正在生成...';
            
            // 更新测试链接显示
            document.getElementById('test-url').textContent = data;
            
            const img = document.createElement('img');
            img.src = 'qr_generator.php?size=200&data=' + encodeURIComponent(data) + '&t=' + Date.now();
            
            img.onload = function() {
                status.innerHTML = '<span class="success">✓ 自定义二维码生成成功</span>';
                console.log('自定义二维码生成成功');
            };
            
            img.onerror = function() {
                status.innerHTML = '<span class="error">✗ 自定义二维码生成失败</span>';
                console.error('自定义二维码生成失败');
            };
            
            container.appendChild(img);
        }

        function testPHPGenerator() {
            const container = document.getElementById('php-qr');
            const status = document.getElementById('php-status');
            
            container.innerHTML = '';
            status.innerHTML = '正在测试PHP生成器...';
            
            const img = document.createElement('img');
            img.src = 'qr_generator.php?size=200&data=' + encodeURIComponent(defaultTestData) + '&t=' + Date.now();
            
            img.onload = function() {
                status.innerHTML = '<span class="success">✓ PHP生成器工作正常</span>';
                console.log('PHP生成器测试成功');
                
                // 显示图片信息
                const info = document.createElement('div');
                info.className = 'info';
                info.innerHTML = `图片尺寸: ${this.naturalWidth}x${this.naturalHeight}px`;
                container.appendChild(info);
            };
            
            img.onerror = function() {
                status.innerHTML = '<span class="error">✗ PHP生成器测试失败</span>';
                console.error('PHP生成器测试失败');
            };
            
            container.appendChild(img);
        }

        function testQRServer() {
            const container = document.getElementById('qr-server');
            container.innerHTML = '';
            
            const img = document.createElement('img');
            img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' + encodeURIComponent(defaultTestData);
            
            img.onload = function() {
                console.log('QR Server API 测试成功');
            };
            
            img.onerror = function() {
                container.innerHTML = '<span class="error">服务不可用</span>';
                console.error('QR Server API 测试失败');
            };
            
            container.appendChild(img);
        }

        function testGoogleCharts() {
            const container = document.getElementById('google-charts');
            container.innerHTML = '';
            
            const img = document.createElement('img');
            img.src = 'https://chart.googleapis.com/chart?chs=150x150&cht=qr&chl=' + encodeURIComponent(defaultTestData);
            
            img.onload = function() {
                console.log('Google Charts API 测试成功');
            };
            
            img.onerror = function() {
                container.innerHTML = '<span class="error">服务不可用</span>';
                console.error('Google Charts API 测试失败');
            };
            
            container.appendChild(img);
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始自动测试');
            
            // 自动生成默认二维码
            setTimeout(function() {
                generateCustomQR();
                testPHPGenerator();
            }, 500);
        });
    </script>
</body>
</html>
