<?php
// 测试Mapi接口配置
header('Content-Type: application/json; charset=utf-8');

// 引入易支付配置
$config = include 'epay_config.php';

echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "    <meta charset='utf-8'>\n";
echo "    <title>Mapi接口测试</title>\n";
echo "    <style>\n";
echo "        body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo "        .config-item { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 5px; }\n";
echo "        .test-result { margin: 20px 0; padding: 15px; border-radius: 5px; }\n";
echo "        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }\n";
echo "        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }\n";
echo "        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";
echo "    <h1>Mapi接口配置测试</h1>\n";

// 显示当前配置
echo "    <h2>当前配置信息</h2>\n";
echo "    <div class='config-item'><strong>商户ID:</strong> " . $config['pid'] . "</div>\n";
echo "    <div class='config-item'><strong>商户密钥:</strong> " . substr($config['key'], 0, 8) . "..." . "</div>\n";
echo "    <div class='config-item'><strong>Mapi接口地址:</strong> " . $config['mapi_url'] . "</div>\n";
echo "    <div class='config-item'><strong>通知地址:</strong> " . $config['notify_url'] . "</div>\n";
echo "    <div class='config-item'><strong>返回地址:</strong> " . $config['return_url'] . "</div>\n";

// 测试接口连通性
echo "    <h2>接口连通性测试</h2>\n";

$testUrl = $config['mapi_url'];
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "    <div class='test-result error'><strong>连接失败:</strong> " . $error . "</div>\n";
} elseif ($httpCode !== 200) {
    echo "    <div class='test-result error'><strong>HTTP错误:</strong> 状态码 " . $httpCode . "</div>\n";
} else {
    echo "    <div class='test-result success'><strong>连接成功:</strong> 接口可正常访问</div>\n";
    echo "    <div class='test-result info'><strong>接口响应:</strong> " . htmlspecialchars(substr($response, 0, 200)) . "...</div>\n";
}

// 生成签名函数
function getSign($params, $key) {
    // 1. 过滤空值和签名参数
    $params = array_filter($params, function($value, $k) {
        return $value !== '' && $value !== null && $k != 'sign' && $k != 'sign_type';
    }, ARRAY_FILTER_USE_BOTH);

    // 2. 按照参数名ASCII码从小到大排序
    ksort($params);

    // 3. 拼接成URL键值对
    $stringA = '';
    foreach ($params as $k => $v) {
        $stringA .= "{$k}={$v}&";
    }
    $stringA = rtrim($stringA, '&');

    // 4. 拼接商户密钥并进行MD5加密
    $stringSignTemp = $stringA . $key;
    return md5($stringSignTemp);
}

// 测试签名生成
echo "    <h2>签名生成测试</h2>\n";

$testParams = [
    'pid' => $config['pid'],
    'type' => 'alipay',
    'out_trade_no' => 'TEST' . date('YmdHis'),
    'name' => '测试商品',
    'money' => '0.01'
];

$testSign = getSign($testParams, $config['key']);
echo "    <div class='test-result info'><strong>测试参数:</strong> " . json_encode($testParams, JSON_UNESCAPED_UNICODE) . "</div>\n";
echo "    <div class='test-result info'><strong>生成签名:</strong> " . $testSign . "</div>\n";

// 模拟支付请求测试
echo "    <h2>模拟支付请求测试</h2>\n";

$params = [
    'pid' => $config['pid'],
    'type' => 'alipay',
    'out_trade_no' => 'TEST' . date('YmdHis') . rand(100, 999),
    'notify_url' => $config['notify_url'],
    'return_url' => $config['return_url'],
    'name' => '测试商品',
    'money' => '0.01',
    'clientip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
    'device' => 'pc'
];

$sign = getSign($params, $config['key']);
$params['sign'] = $sign;
$params['sign_type'] = 'MD5';

echo "    <div class='test-result info'><strong>请求参数:</strong><br><pre>" . json_encode($params, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre></div>\n";

// 发送测试请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $config['mapi_url']);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "    <div class='test-result error'><strong>请求失败:</strong> " . $error . "</div>\n";
} elseif ($httpCode !== 200) {
    echo "    <div class='test-result error'><strong>HTTP错误:</strong> 状态码 " . $httpCode . "</div>\n";
} else {
    $result = json_decode($response, true);
    if ($result) {
        if ($result['code'] == 1) {
            echo "    <div class='test-result success'><strong>测试成功:</strong> 支付订单创建成功</div>\n";
            echo "    <div class='test-result info'><strong>返回数据:</strong><br><pre>" . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre></div>\n";
            
            // 检查返回的支付方式
            if (isset($result['payurl'])) {
                echo "    <div class='test-result success'><strong>支付方式:</strong> 跳转支付 (payurl)</div>\n";
            } elseif (isset($result['qrcode'])) {
                echo "    <div class='test-result success'><strong>支付方式:</strong> 二维码支付 (qrcode)</div>\n";
            } elseif (isset($result['urlscheme'])) {
                echo "    <div class='test-result success'><strong>支付方式:</strong> 小程序支付 (urlscheme)</div>\n";
            }
        } else {
            echo "    <div class='test-result error'><strong>接口返回错误:</strong> " . ($result['msg'] ?? '未知错误') . "</div>\n";
            echo "    <div class='test-result info'><strong>完整响应:</strong><br><pre>" . json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre></div>\n";
        }
    } else {
        echo "    <div class='test-result error'><strong>响应解析失败:</strong> 返回数据不是有效的JSON格式</div>\n";
        echo "    <div class='test-result info'><strong>原始响应:</strong> " . htmlspecialchars($response) . "</div>\n";
    }
}

echo "    <h2>测试完成</h2>\n";
echo "    <p>如果所有测试都通过，说明Mapi接口配置正确，可以正常使用。</p>\n";
echo "</body>\n";
echo "</html>\n";
?>
