<?php
/**
 * 易支付异步通知处理
 */

// 引入易支付配置
$config = include 'epay_config.php';

// 记录通知日志
$logData = [
    'time' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'get' => $_GET,
    'post' => $_POST,
    'raw_input' => file_get_contents('php://input')
];
file_put_contents('payment_notify.log', json_encode($logData) . "\n", FILE_APPEND);

/**
 * 验证签名
 */
function verifySign($params, $key) {
    // 过滤空值和签名参数
    $params = array_filter($params, function($value, $k) {
        return $value !== '' && $value !== null && $k != 'sign' && $k != 'sign_type';
    }, ARRAY_FILTER_USE_BOTH);

    // 按照参数名ASCII码从小到大排序
    ksort($params);

    // 拼接成URL键值对
    $stringA = '';
    foreach ($params as $k => $v) {
        $stringA .= "{$k}={$v}&";
    }
    $stringA = rtrim($stringA, '&');

    // 拼接商户密钥并进行MD5加密
    $stringSignTemp = $stringA . $key;
    return md5($stringSignTemp);
}

// 获取通知参数
$params = $_GET + $_POST;

// 验证必要参数（移除name参数）
$requiredParams = ['pid', 'trade_no', 'out_trade_no', 'type', 'money', 'trade_status', 'sign'];
foreach ($requiredParams as $param) {
    if (!isset($params[$param])) {
        error_log("epay_notify.php 缺少参数: " . $param);
        exit('fail');
    }
}

// 验证商户ID
if ($params['pid'] != $config['pid']) {
    exit('fail');
}

// 验证签名
$sign = verifySign($params, $config['key']);
if ($sign !== $params['sign']) {
    exit('fail');
}

// 验证支付状态
if ($params['trade_status'] !== 'TRADE_SUCCESS') {
    exit('fail');
}

// 连接数据库
$servername = "localhost";
$username = "root";
$password = "1233456";
$dbname = "fbsbs";

$conn = new mysqli($servername, $username, $password, $dbname);
$conn->set_charset("utf8");

if ($conn->connect_error) {
    error_log("数据库连接失败: " . $conn->connect_error);
    exit('fail');
}

try {
    // 记录开始处理
    error_log("epay_notify.php 开始处理支付通知");

    // 解析业务参数
    $paramData = json_decode($params['param'] ?? '{}', true);
    $user = $paramData['user'] ?? '';
    $package = $paramData['package'] ?? '';

    error_log("解析的用户信息: user=" . $user . ", package=" . $package);

    if (empty($user) || empty($package)) {
        error_log("用户信息或套餐信息为空");
        exit('fail');
    }
    
    // 检查订单是否已经处理过
    $checkStmt = $conn->prepare("SELECT * FROM payment_log WHERE out_trade_no = ? AND status = 1");
    $checkStmt->bind_param("s", $params['out_trade_no']);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        // 订单已处理，直接返回成功
        exit('success');
    }
    
    // 验证用户是否存在
    $userStmt = $conn->prepare("SELECT * FROM user WHERE user = ?");
    $userStmt->bind_param("s", $user);
    $userStmt->execute();
    $userResult = $userStmt->get_result();

    if ($userResult->num_rows === 0) {
        error_log("用户不存在: " . $user);
        exit('fail');
    }

    $userInfo = $userResult->fetch_assoc();
    $currentVip = $userInfo['vip'];
    error_log("用户当前VIP状态: " . $currentVip);
    
    // 计算新的VIP到期日期
    $currentDate = date('Y-m-d');
    $startDate = ($currentVip > $currentDate) ? $currentVip : $currentDate;

    error_log("VIP计算: 当前日期=" . $currentDate . ", 开始日期=" . $startDate . ", 套餐=" . $package);

    switch ($package) {
        case '1': // 月套餐
            $newVipDate = date('Y-m-d', strtotime($startDate . ' +1 month'));
            break;
        case '3': // 季套餐
            $newVipDate = date('Y-m-d', strtotime($startDate . ' +3 months'));
            break;
        case 'year': // 年套餐
            $newVipDate = date('Y-m-d', strtotime($startDate . ' +1 year'));
            break;
        case 'forever': // 永久套餐
            $newVipDate = '2099-12-31';
            break;
        default:
            error_log("未知的套餐类型: " . $package);
            exit('fail');
    }

    error_log("计算的新VIP日期: " . $newVipDate);
    
    // 开始事务
    $conn->begin_transaction();
    
    try {
        // 更新用户VIP信息（不自动开启自动同步）
        $updateStmt = $conn->prepare("UPDATE user SET vip = ? WHERE user = ?");
        $updateStmt->bind_param("ss", $newVipDate, $user);
        $updateResult = $updateStmt->execute();

        if (!$updateResult) {
            error_log("更新用户VIP失败: " . $conn->error);
            throw new Exception("更新用户VIP失败");
        }

        $affectedRows = $updateStmt->affected_rows;
        error_log("更新用户VIP成功，影响行数: " . $affectedRows);

        // 记录支付日志
        $logStmt = $conn->prepare("INSERT INTO payment_log (user, package_type, price, vip_date, payment_time, out_trade_no, trade_no, status) VALUES (?, ?, ?, ?, NOW(), ?, ?, 1)");
        $logStmt->bind_param("ssssss", $user, $package, $params['money'], $newVipDate, $params['out_trade_no'], $params['trade_no']);
        $logResult = $logStmt->execute();

        if (!$logResult) {
            error_log("记录支付日志失败: " . $conn->error);
            throw new Exception("记录支付日志失败");
        }

        error_log("支付日志记录成功");

        // 提交事务
        $conn->commit();

        error_log("支付处理完成，用户 " . $user . " 的VIP已更新为 " . $newVipDate);

        // 返回成功
        exit('success');
        
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollback();
        exit('fail');
    }
    
} catch (Exception $e) {
    exit('fail');
}

$conn->close();
?>
