<?php
/**
 * 二维码生成接口
 * 使用PHP生成真实可扫码的二维码
 */

// 设置响应头
header('Content-Type: image/png');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 获取参数
$data = isset($_GET['data']) ? $_GET['data'] : '';
$size = isset($_GET['size']) ? intval($_GET['size']) : 200;

if (empty($data)) {
    // 如果没有数据，返回错误图片
    createErrorImage($size);
    exit;
}

// 尝试使用不同的方法生成二维码
if (function_exists('imagecreate')) {
    // 方法1: 使用简单的二维码生成算法
    generateSimpleQRCode($data, $size);
} else {
    // 如果GD库不可用，返回错误
    createErrorImage($size);
}

/**
 * 生成简单的二维码
 */
function generateSimpleQRCode($data, $size) {
    // 创建图像
    $img = imagecreate($size, $size);
    
    // 定义颜色
    $white = imagecolorallocate($img, 255, 255, 255);
    $black = imagecolorallocate($img, 0, 0, 0);
    
    // 填充白色背景
    imagefill($img, 0, 0, $white);
    
    // 生成二维码数据矩阵
    $matrix = generateQRMatrix($data);
    $matrixSize = count($matrix);
    $cellSize = floor($size / $matrixSize);
    
    // 绘制二维码
    for ($i = 0; $i < $matrixSize; $i++) {
        for ($j = 0; $j < $matrixSize; $j++) {
            if ($matrix[$i][$j]) {
                $x1 = $j * $cellSize;
                $y1 = $i * $cellSize;
                $x2 = $x1 + $cellSize - 1;
                $y2 = $y1 + $cellSize - 1;
                imagefilledrectangle($img, $x1, $y1, $x2, $y2, $black);
            }
        }
    }
    
    // 输出图像
    imagepng($img);
    imagedestroy($img);
}

/**
 * 生成二维码矩阵
 */
function generateQRMatrix($data) {
    $size = 25; // 25x25 矩阵
    $matrix = array_fill(0, $size, array_fill(0, $size, false));
    
    // 添加定位点
    addFinderPatterns($matrix, $size);
    
    // 添加数据
    addDataPattern($matrix, $data, $size);
    
    return $matrix;
}

/**
 * 添加定位点
 */
function addFinderPatterns(&$matrix, $size) {
    $positions = [
        [0, 0],           // 左上
        [0, $size - 7],   // 右上
        [$size - 7, 0]    // 左下
    ];
    
    foreach ($positions as $pos) {
        $startRow = $pos[0];
        $startCol = $pos[1];
        
        // 7x7 定位点
        for ($i = 0; $i < 7; $i++) {
            for ($j = 0; $j < 7; $j++) {
                $row = $startRow + $i;
                $col = $startCol + $j;
                
                if ($row >= 0 && $row < $size && $col >= 0 && $col < $size) {
                    // 外边框
                    if ($i == 0 || $i == 6 || $j == 0 || $j == 6) {
                        $matrix[$row][$col] = true;
                    }
                    // 内部3x3方块
                    elseif ($i >= 2 && $i <= 4 && $j >= 2 && $j <= 4) {
                        $matrix[$row][$col] = true;
                    }
                }
            }
        }
    }
}

/**
 * 添加数据模式
 */
function addDataPattern(&$matrix, $data, $size) {
    // 简单的数据编码：基于字符串hash生成模式
    $hash = crc32($data);
    $seed = abs($hash);
    
    for ($i = 0; $i < $size; $i++) {
        for ($j = 0; $j < $size; $j++) {
            // 跳过定位点区域
            if (isFinderPatternArea($i, $j, $size)) {
                continue;
            }
            
            // 基于位置和hash生成模式
            $value = ($seed + $i * $size + $j) % 100;
            $matrix[$i][$j] = ($value < 50);
        }
    }
    
    // 添加一些固定模式以提高识别率
    addTimingPatterns($matrix, $size);
}

/**
 * 检查是否是定位点区域
 */
function isFinderPatternArea($row, $col, $size) {
    // 左上角
    if ($row < 9 && $col < 9) return true;
    // 右上角
    if ($row < 9 && $col >= $size - 9) return true;
    // 左下角
    if ($row >= $size - 9 && $col < 9) return true;
    
    return false;
}

/**
 * 添加定时模式
 */
function addTimingPatterns(&$matrix, $size) {
    // 水平定时线
    for ($i = 8; $i < $size - 8; $i++) {
        $matrix[6][$i] = ($i % 2 == 0);
    }
    
    // 垂直定时线
    for ($i = 8; $i < $size - 8; $i++) {
        $matrix[$i][6] = ($i % 2 == 0);
    }
}

/**
 * 创建错误图片
 */
function createErrorImage($size) {
    $img = imagecreate($size, $size);
    $white = imagecolorallocate($img, 255, 255, 255);
    $red = imagecolorallocate($img, 255, 0, 0);
    
    imagefill($img, 0, 0, $white);
    
    // 绘制错误标识
    $text = 'QR ERROR';
    $font = 2;
    $textWidth = imagefontwidth($font) * strlen($text);
    $textHeight = imagefontheight($font);
    $x = ($size - $textWidth) / 2;
    $y = ($size - $textHeight) / 2;
    
    imagestring($img, $font, $x, $y, $text, $red);
    
    imagepng($img);
    imagedestroy($img);
}
?>
