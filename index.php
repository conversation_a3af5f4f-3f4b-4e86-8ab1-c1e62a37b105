<?php
// $dbconfig=array('host' => 'localhost','port' => 3306,'user' => 'root','pwd' => '123456','dbname' => 'fbsbs');
$dbconfig=array('host' => 'localhost','port' => 3306,'user' => 'fbsbs','pwd' => 'fbsbsxcx...','dbname' => 'fbsbs');
//数据库账号密码↑
$host = $dbconfig["host"];$port = $dbconfig["port"];$user = $dbconfig["user"];$pwd = $dbconfig["pwd"];$dbname = $dbconfig["dbname"];
//数据库账号密码↑
$conn = mysqli_connect($host,$user,$pwd,$dbname,$port);
//连接数据库
if (!$conn){die("数据库连接失败");}
$conn=$GLOBALS['conn'];
$key ='apolkj*2$*3!~9s3';

$iv = 'eres12120ws+3s10';

$data = $_POST['data'];

// setcookie('user', '', 0);

$ggsc = 0;

if(!empty($data)){
	
	$data = openssl_decrypt($data, 'AES-128-CBC', $key, 0,$iv);
	
	$sj = date("H:i:s");
	
	$data = json_decode($data,320);
	
	if($data['lx']=='dl'&&$data['sj']>$sj){
		if($data['user']!=''&&$data['pass']!=''){
			
			$user = $data['user'];
			
			$pass = $data['pass'];
			
			$data = curl_get('https://bs.679l.cn/qjbs.php?user='.$user.'&pass='.$pass.'&count=521');
			
			$data = json_decode($data,320);
			
			if($data['code']==200){
				
				
				$data = mysqli_query($conn,"SELECT * FROM user WHERE `user`='$user'");
				if(mysqli_num_rows($data)){
					$tj="update `user` set `pass` ='{$pass}' where `user`='{$user}'";
					if($conn->query($tj)){
						$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
						$res = $rs->fetch_assoc();
						
						$sj = date("Y-m-d");
						
						if($sj<$res['vip']){
							$vip = 1;
							$zdsb = $res['code'];
						}else{
							$vip = 0;
							$zdsb = 0;
						}
						
						
						if(strlen($res['xcx'])>18){
							$xcx = 1;
						}else{
							$xcx = 0;
						}
						
						
						$code = 1;
						$arr = array('user'=>$user,'pass'=>$pass);
						$arr = openssl_encrypt(json_encode($arr,320), 'AES-128-CBC', $key, 0,$iv);
						setcookie('user',$arr,time()+(365 * 24 * 60 * 60));
						echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "登录成功",type: "success",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
					}else{
						echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "登录失败 请重试",type: "error",confirmButtonText: "确定"});</script></body></html>';
						$code = 2;
						$zdsb = 0;
						$vip = 0;
						$xcx = 1;
					}
				}else{
					$sj=date('Y-m-d');
					$gn = date('Y-m-d H:i:s', strtotime('+0minute'));
					if(mysqli_query($conn,"INSERT INTO `user` (`user`, `pass`, `vip`,`gnvip`, `code`, `bs`, `xcx`) VALUES ('$user','$pass','$sj','$gn','0','0','')")){
						$arr = array('user'=>$user,'pass'=>$pass);
						$arr = openssl_encrypt(json_encode($arr,320), 'AES-128-CBC', $key, 0,$iv);
						setcookie('user',$arr,time()+(365 * 24 * 60 * 60));
						$code = 1;
						$vip = 0;
						$zdsb = 0;
						$xcx = 0;
						echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "登录成功",type: "success",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
					}else{
						echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "登录失败 请重试",type: "error",confirmButtonText: "确定"});</script></body></html>';
						$code = 2;
						$zdsb = 0;
						$vip = 0;
						$xcx = 1;
					}
				}
				
				
				
			}else{
				echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "'.$data['msg'].'",type: "error",confirmButtonText: "确定"});</script></body></html>';
				$code = 2;
				$zdsb = 0;
				$vip = 0;
				$xcx = 1;
			}
			
			
			
			
		}else{
			echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "安全校验失败 请重试~",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
			$code = 2;
			$zdsb = 0;
			$vip = 0;
			$xcx = 1;
		}
	}else if($data['lx']=='sbs'&&$data['sj']>$sj){
		
		if(!empty($_COOKIE['user'])){
			
			$dat = openssl_decrypt($_COOKIE['user'], 'AES-128-CBC', $key, 0,$iv);
			
			$dat = json_decode($dat,320);
			
			$user = $dat['user']; $pass = $dat['pass']; $bs = $data['rlz'];
			
			$dataa = mysqli_query($conn,"SELECT * FROM `user` WHERE `user`='$user'");
			if(mysqli_num_rows($dataa)){
			    
			    $rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
				$res = $rs->fetch_assoc();
				
				if($res['vip']>date('Y-m-d')){
				    if($data['rlz']>0&&$data['rlz']<=99880){
					   
					   $data = curl_get('https://bs.679l.cn/qjbs.php?user='.$user.'&pass='.$pass.'&count='.$bs);
					   
					   $data = json_decode($data,320);
					   
					   if($data['code']==200){
						 $rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
						 $res = $rs->fetch_assoc();
						 
						 $sj = date("Y-m-d");
						 
						 if($sj<$res['vip']){
						 	$vip = 1;
						 	$zdsb = $res['code'];
						 }else{
						 	$vip = 0;
						 	$zdsb = 0;
						 }
						 
						 
						 if(strlen($res['xcx'])>18){
						 	$xcx = 1;
						 }else{
						 	$xcx = 0;
						 }
						 
						 $code = 1;
						 
						 
						 setcookie('sc',$bs,time()+(365 * 24 * 60 * 60));
						 $code = 1;
						 echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">
						swal({title: "同步成功",text:"'.$bs.'步",type: "success",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){});</script></body></html>';
					   }else if($data['code']==201){
						   
						  setcookie('user', '', 0);
						  $code = 2;
						  $zdsb = 0;
						  $xcx = 1;
						  $vip = 0;
						  echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "'.$data['msg'].'",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
						  
					   }else{
						   $rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
						   $res = $rs->fetch_assoc();
						   
						   $sj = date("Y-m-d");
						   
						   if($sj<$res['vip']){
						   	$vip = 1;
						   	$zdsb = $res['code'];
						   }else{
						   	$vip = 0;
						   	$zdsb = 0;
						   }
						   $code = 1;
						   
						   if(strlen($res['xcx'])>18){
						   							$xcx = 1;
						   						}else{
						   							$xcx = 0;
						   						}
						   
						   
						   
						   
						   echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "安全校验失败 请重试~",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
					   }
					
					   
				}else{
						
					$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
					$res = $rs->fetch_assoc();
					$sj = date("Y-m-d");
					if($sj<$res['vip']){
						$vip = 1;
						$zdsb = $res['code'];
					}else{
						$vip = 0;
						$zdsb = 0;
					}
					
					if(strlen($res['xcx'])>18){
												$xcx = 1;
											}else{
												$xcx = 0;
											}
					
					$code = 1;
					echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal("请输入正确的数量","1-99880 [建议5w左右]","error");</script></body></html>';
					   
				}
				}else if($res['gnvip']>date('Y-m-d H:i:s')){
				    
				    if($data['rlz']>0&&$data['rlz']<=99880){
					   
					   $data = curl_get('https://bs.679l.cn/qjbs.php?user='.$user.'&pass='.$pass.'&count='.$bs);
					   
					   $data = json_decode($data,320);
					   
					   if($data['code']==200){
						 $rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
						 $res = $rs->fetch_assoc();
						 
						 $sj = date("Y-m-d");
						 
						 if($sj<$res['vip']){
						 	$vip = 1;
						 	$zdsb = $res['code'];
						 }else{
						 	$vip = 0;
						 	$zdsb = 0;
						 }
						 $code = 1;
						 
						 if(strlen($res['xcx'])>18){
						 	$xcx = 1;
						 }else{
						 	$xcx = 0;
						 }
						 
						 
						 setcookie('sc',$bs,time()+(365 * 24 * 60 * 60));
						 $code = 1;
						 echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">
						swal({title: "同步成功",text:"'.$bs.'步",type: "success",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){});</script></body></html>';
					   }else if($data['code']==201){
						   
						  setcookie('user', '', 0);
						  $code = 2;
						  $zdsb = 0;
						  $xcx = 1;
						  $vip = 0;
						  echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "'.$data['msg'].'",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
						  
					   }else{
						   $rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
						   $res = $rs->fetch_assoc();
						   
						   $sj = date("Y-m-d");
						   
						   if($sj<$res['vip']){
						   	$vip = 1;
						   	$zdsb = $res['code'];
						   }else{
						   	$vip = 0;
						   	$zdsb = 0;
						   }
						   $code = 1;
						   
						   if(strlen($res['xcx'])>18){
						   							$xcx = 1;
						   						}else{
						   							$xcx = 0;
						   						}
						   
						   
						   
						   
						   echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "安全校验失败 请重试~",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
					   }
					
					   
				}else{
						
					$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
					$res = $rs->fetch_assoc();
					$sj = date("Y-m-d");
					if($sj<$res['vip']){
						$vip = 1;
						$zdsb = $res['code'];
					}else{
						$vip = 0;
						$zdsb = 0;
					}
					
					$code = 1;
					
					if(strlen($res['xcx'])>18){
												$xcx = 1;
											}else{
												$xcx = 0;
											}
					
					$code = 1;
					echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal("请输入正确的数量","1-99880 [建议5w左右]","error");</script></body></html>';
					   
				}
				    
				}else{
				        $rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
						 $res = $rs->fetch_assoc();
						 
						 $sj = date("Y-m-d");
						 
						 if($sj<$res['vip']){
						 	$vip = 1;
						 	$zdsb = $res['code'];
						 }else{
						 	$vip = 0;
						 	$zdsb = 0;
						 }
						 
						 
						 if(strlen($res['xcx'])>18){
						 	$xcx = 1;
						 }else{
						 	$xcx = 0;
						 }
						 
						 $ggsc = 1;
						 $code = 1;
						 
				}
				
				
				
				
			}else{
				setcookie('user', '', 0);
				echo '<script type="text/javascript">window.location.href = "/"</script>';
			}
				
				
		}else{
			setcookie('user', '', 0);
			echo '<script type="text/javascript">window.location.href = "/"</script>';
		}
		
		
	}else if($data['lx']=='ydkq'&&$data['sj']>$sj){
		
		if(!empty($_COOKIE['user'])){
			
			$dat = openssl_decrypt($_COOKIE['user'], 'AES-128-CBC', $key, 0,$iv);
			
			$dat = json_decode($dat,320);
			
			$user = $dat['user']; $pass = $dat['pass']; $bs = $data['rlz'];
			
			$dataa = mysqli_query($conn,"SELECT * FROM `user` WHERE `user`='$user'");
			if(mysqli_num_rows($dataa)){
				
				if($data['rlz']>0&&$data['rlz']<=99880){
					 
					$data = curl_get('https://bs.679l.cn/qjbs.php?user='.$user.'&pass='.$pass.'&count=521');
					   
					$data = json_decode($data,320);
					   
					if($data['code']==200){
						 
						$tj="update `user` set `code` ='1',`bs`='{$bs}' where `user`='{$user}'";
						if($conn->query($tj)){
							$vip = 1;
							$zdsb = 1;
							$code = 1;
							$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
							$res = $rs->fetch_assoc();
							if(strlen($res['xcx'])>18){
														$xcx = 1;
													}else{
														$xcx = 0;
													}
							echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "自动执行开启成功",type: "success",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){});</script></body></html>';
						}else{
							$vip = 1;
							$zdsb = 0;
							$code = 1;
							$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
							$res = $rs->fetch_assoc();
							if(strlen($res['xcx'])>18){
														$xcx = 1;
													}else{
														$xcx = 0;
													}
							echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "安全校验失败 请重试~",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
							
						}
							 
					}else if($data['code']==201){
						   
						setcookie('user', '', 0);
						$code = 2;
						$zdsb = 0;
						$vip = 0;
						$xcx = 1;
						echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "'.$data['msg'].'",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
						  
					}else{
						$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
						$res = $rs->fetch_assoc();
						
						$sj = date("Y-m-d");
						
						if($sj<$res['vip']){
							$vip = 1;
							$zdsb = $res['code'];
						}else{
							$vip = 0;
							$zdsb = 0;
						}
						if(strlen($res['xcx'])>18){
													$xcx = 1;
												}else{
													$xcx = 0;
												}
						$code = 1;
						echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "安全校验失败 请重试~",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
					   }
					
					   
				}else{
					   $rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
					   $res = $rs->fetch_assoc();
					   $sj = date("Y-m-d");
					   if($sj<$res['vip']){
					   	$vip = 1;
					   	$zdsb = $res['code'];
					   }else{
					   	$vip = 0;
					   	$zdsb = 0;
					   }
					   $code = 1;
					   if(strlen($res['xcx'])>18){
					   							$xcx = 1;
					   						}else{
					   							$xcx = 0;
					   						}
					   echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal("请输入正确的数量","1-99880 [建议5w左右]","error");</script></body></html>';
				}
			}else{
					setcookie('user', '', 0);
					echo '<script type="text/javascript">window.location.href = "/"</script>';
			}
				
		}else{
				setcookie('user', '', 0);
				echo '<script type="text/javascript">window.location.href = "/"</script>';
		}
		
	}else if($data['lx']=='ydgb'&&$data['sj']>$sj){
		
		if(!empty($_COOKIE['user'])){
		   // Cookie 存在且值不为空
		   
		   $dat = openssl_decrypt($_COOKIE['user'], 'AES-128-CBC', $key, 0,$iv);
		   
		   $dat = json_decode($dat,320);
		   
		   $user = $dat['user'];
		   
		   $data = mysqli_query($conn,"SELECT * FROM user WHERE `user`='$user'");
		   if(mysqli_num_rows($data)){
		   					 
				$tj="update `user` set `code` ='0',`bs`='0' where `user`='{$user}'";
		   		if($conn->query($tj)){
					
		   			$vip = 1;
		   			$zdsb = 0;
		   			$code = 1;
					$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
												$res = $rs->fetch_assoc();
												if(strlen($res['xcx'])>18){
																			$xcx = 1;
																		}else{
																			$xcx = 0;
																		}
		   			echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">
		   					 	swal({title: "自动执行已关闭",type: "success",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){});</script></body></html>';
		   		
				}else{
		   			$vip = 1;
		   			$zdsb = 1;
		   			$code = 1;
					$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
												$res = $rs->fetch_assoc();
												if(strlen($res['xcx'])>18){
																			$xcx = 1;
																		}else{
																			$xcx = 0;
																		}
					echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "安全校验失败 请重试~",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
		   		}
		   					 
		   	}else{
				
		   		setcookie('user', '', 0);
		   		echo '<script type="text/javascript">window.location.href = "/"</script>';
		   	
			}
		   
		   
		}else{
			setcookie('user', '', 0);
			echo '<script type="text/javascript">window.location.href = "/"</script>';
		}
		
	}else if($data['lx']=='xcx'&&$data['sj']>$sj){
		
		if(!empty($_COOKIE['user'])){
		   // Cookie 存在且值不为空
		   
		   $xcxid = $data['xcxid'];
		   
		   $dat = openssl_decrypt($_COOKIE['user'], 'AES-128-CBC', $key, 0,$iv);
		   
		   $dat = json_decode($dat,320);
		   
		   $user = $dat['user'];
		   
		   $data = mysqli_query($conn,"SELECT * FROM user WHERE `user`='$user'");
		   if(mysqli_num_rows($data)){
			   
			   
			   if(strlen($xcxid)>18){
			       
			        $data = mysqli_query($conn,"SELECT * FROM xcxuser WHERE `user`='$xcxid'");
		            if(mysqli_num_rows($data)){
		                
		                $rs=$conn->query("SELECT * FROM xcxuser WHERE `user`='$xcxid'");
				   	    $res = $rs->fetch_assoc();
				   	    if($res['zt']==0){
				   	        
				   	        $tj="update `user` set `xcx` ='$xcxid' where `user`='{$user}'";
				   if($conn->query($tj)){
				       
				       $tj="update `xcxuser` set `zt` ='1' where `user`='{$xcxid}'";
				       $conn->query($tj);
				   	
				   	$vip = 1;
				   	$zdsb = 0;
				   	$code = 1;
					$xcx = 1;
					
				   	echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">
				   			 	swal({title: "绑定成功",type: "success",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){});</script></body></html>';
				   
				   }else{
				   	$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
				   	$res = $rs->fetch_assoc();
				   	$sj = date("Y-m-d");
				   	if($sj<$res['vip']){
				   		$vip = 1;
				   		$zdsb = $res['code'];
				   	}else{
				   		$vip = 0;
				   		$zdsb = $res['code'];
				   	}
				   	$code = 1;
					$xcx = 0;
					
					
				   	echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "安全校验失败 请重试~",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
				   }
				   	        
				   	    }else{
				   	         echo '<script type="text/javascript">alert("id已被使用或已存在");window.location.href = "/"</script>';
				   	    }
		                
				   
		            
		            }else{
		                
		                echo '<script type="text/javascript">alert("id已被使用或已存在");window.location.href = "/"</script>';
				   
		            }
				   
				   
			   }else{
				   
				   echo '<script type="text/javascript">alert("id错误");window.location.href = "/"</script>';
				   
			   }
		   					 
				
		   					 
		   	}else{
				
		   		setcookie('user', '', 0);
		   		echo '<script type="text/javascript">window.location.href = "/"</script>';
		   	
			}
		   
		   
		}else{
			setcookie('user', '', 0);
			echo '<script type="text/javascript">window.location.href = "/"</script>';
		}
		
	}else if($data['lx']=='xcxhb'&&$data['sj']>$sj){
		
		if(!empty($_COOKIE['user'])){
		   // Cookie 存在且值不为空
		   
		   $xcxid = $data['xcxid'];
		   
		   $dat = openssl_decrypt($_COOKIE['user'], 'AES-128-CBC', $key, 0,$iv);
		   
		   $dat = json_decode($dat,320);
		   
		   $user = $dat['user'];
		   
		   $data = mysqli_query($conn,"SELECT * FROM user WHERE `user`='$user'");
		   if(mysqli_num_rows($data)){
			   
			   
			   if(strlen($xcxid)>18){
			       
			        $data = mysqli_query($conn,"SELECT * FROM xcxuser WHERE `user`='$xcxid'");
		            if(mysqli_num_rows($data)){
		                
		                $rs=$conn->query("SELECT * FROM xcxuser WHERE `user`='$xcxid'");
				   	    $res = $rs->fetch_assoc();
				   	    if($res['zt']==0){
				   	        
				   	        $tj="update `user` set `xcx` ='$xcxid' where `user`='{$user}'";
				   if($conn->query($tj)){
				       
				       $tj="update `xcxuser` set `zt` ='1' where `user`='{$xcxid}'";
				       $conn->query($tj);
				   	
				   	$vip = 1;
				   	$zdsb = 0;
				   	$code = 1;
					$xcx = 1;
					
				   	echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">
				   			 	swal({title: "绑定成功",type: "success",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){});</script></body></html>';
				   
				   }else{
				   	$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
				   	$res = $rs->fetch_assoc();
				   	$sj = date("Y-m-d");
				   	if($sj<$res['vip']){
				   		$vip = 1;
				   		$zdsb = $res['code'];
				   	}else{
				   		$vip = 0;
				   		$zdsb = $res['code'];
				   	}
				   	$code = 1;
					$xcx = 0;
					
					
				   	echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "安全校验失败 请重试~",type: "error",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){window.location.href = "/"});</script></body></html>';
				   }
				   	        
				   	    }else{
				   	        
				   	        $tj="update `xcxuser` set `zt` ='0'  where `user`='{$xcxid}'";
                            if($conn->query($tj)){
                                if(mysqli_query($conn,"delete from user where `xcx`='$xcxid'")){
				   	             echo '<script type="text/javascript">alert("解绑成功  请重新绑定！");window.location.href = "/"</script>';
				   	            }else{
				   	             echo '<script type="text/javascript">alert("解绑失败 请稍后重试!");window.location.href = "/"</script>';
				   	            }
                            }else{
				   	             echo '<script type="text/javascript">alert("解绑失败 请稍后重试!");window.location.href = "/"</script>';
				   	        }
				   	        
				   	         
				   	         
				   	         
				   	         
				   	         
				   	    }
		                
				   
		            
		            }else{
		                
		                echo '<script type="text/javascript">alert("id不存在");window.location.href = "/"</script>';
				   
		            }
				   
				   
			   }else{
				   
				   echo '<script type="text/javascript">alert("id错误");window.location.href = "/"</script>';
				   
			   }
		   					 
				
		   					 
		   	}else{
				
		   		setcookie('user', '', 0);
		   		echo '<script type="text/javascript">window.location.href = "/"</script>';
		   	
			}
		   
		   
		}else{
			setcookie('user', '', 0);
			echo '<script type="text/javascript">window.location.href = "/"</script>';
		}
		
	}else{
		
		if(!empty($_COOKIE['user'])){
		   // Cookie 存在且值不为空
		   
		   $dat = openssl_decrypt($_COOKIE['user'], 'AES-128-CBC', $key, 0,$iv);
		   
		   $dat = json_decode($dat,320);
		   
		   $user = $dat['user'];
		   
		   $data = mysqli_query($conn,"SELECT * FROM user WHERE `user`='$user'");
		   if(mysqli_num_rows($data)){
		   					 
				$rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
				$res = $rs->fetch_assoc();
				
				$sj = date("Y-m-d");
				
				if($sj<$res['vip']){
					$vip = 1;
					$zdsb = $res['code'];
				}else{
					$vip = 0;
					$zdsb = 0;
				}
				
				if(strlen($res['xcx'])>18){
											$xcx = 1;
										}else{
											$xcx = 0;
										}
				
				$code = 1;
		   					 
		   	}else{
				
		   		setcookie('user', '', 0);
		   		echo '<script type="text/javascript">alert("111");window.location.href = "/"</script>';
		   	
			}
		   
		   
		}else{
			setcookie('user', '', 0);
			echo '<script type="text/javascript">alert("222");window.location.href = "/"</script>';
		}
		
	}
	
}else if(!empty($_COOKIE['user'])){
   // Cookie 存在且值不为空
   
   $data = openssl_decrypt($_COOKIE['user'], 'AES-128-CBC', $key, 0,$iv);
   
   $data = json_decode($data,320);
   
   
   if($data['user']!=''&&$data['pass']!=''){
   	   
	   $user = $data['user']; $pass = $data['pass'];
	   
	   $data = mysqli_query($conn,"SELECT * FROM user WHERE `user`='$user'");
	   if(mysqli_num_rows($data)){
		   
		   // $data = curl_get('http://bs.679l.cn/qjbs.php?user='.$user.'&pass='.$pass.'&count=521');
		   
		   // $data = json_decode($data,320);
		   
		   // if($data['code']==200){
		   	
			 $rs=$conn->query("SELECT * FROM user WHERE `user`='$user'");
			 $res = $rs->fetch_assoc();
			 
			 $sj = date("Y-m-d");
			 
			 if($sj<$res['vip']){
				$vip = 1;
				$zdsb = $res['code'];
			 }else{
				$vip = 0;
				$zdsb = 0;
			 }
			 
			 $code = 1;
			 
			 if(strlen($res['xcx'])>18){
			 	$xcx = 1;
			 }else{
			 	$xcx = 0;
			 }
			 
		   	 echo '<html><link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet"><body><script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script><script type="text/javascript">swal({title: "欢迎回来","text":"24小时为您服务!",type: "success",input: "text",confirmButtonText: "OK",showCancelButton: false,allowOutsideClick: false}, function(inputValue){});</script></body></html>';
		   		
		   // }else{
		   	   
		   // 	  setcookie('user', '', 0);
		   	  
		   // 	  $code = 2;
		   	  
		   // }
		   
	   }else{
		   echo '<script type="text/javascript">alert("222");window.location.href = "/"</script>';
		   setcookie('user', '', 0);
		   
		   $code = 2;
		   $zdsb = 0;
		   $vip = 0;
		   $xcx = 1;
		   
	   }
	   
   }

   
}else{
    // echo '<script type="text/javascript">alert("222");window.location.href = "/"</script>';
   $code = 2;
   $zdsb = 0;
   $xcx = 1;
   $vip = 0;
}




function curl_get($url, &$httpCode = 0) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    //不做证书校验,部署在linux环境下请改为true
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    $file_contents = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    return $file_contents;
}

?>
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no" id="viewport" name="viewport">
		<title>伏笔工具箱网页版</title>
		<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet">
		<style>
			.div{
				    margin: 0px 30px;
				    display: flex;align-items: center;justify-content: center;
					flex-direction: column;
			}
			/* .a>input{
				 height: 88px;
				 text-align: center;
				 margin: 20px 0px;
				 background: #fff;border-radius: 50px;font-size: 36px;
			} */
			.max{
				max-width: 28rem;
				margin: auto;
			}
			/* input{
				display: none;
			} */
			 input:focus {
				            outline: 0px solid slategray;
			                /* border: 0px solid slategray; */
				        }
			
			            input{
			                width: 100%;padding: 12px 0px;border-radius: 12px;border: 1px solid #FFF;text-align: center;background: #f5f5f5;
			            }
			
			
			.shadow {
				/* display: none; */
			    position: fixed;
			    top: 0;
			    bottom: 0;
			    left: 0;
			    right: 0;
			    background: rgba(0,0,0,0.3);
			    z-index: 999;
			}
			.shadow-bg {
			    position: absolute;
			    top: 46%;
			    left: 50%;
			    transform: translate(-50%,-50%);
			    -webkit-transform: translate(-50%,-50%);
			    -moz-transform: translate(-50%,-50%);
			    width: 318px;
			    height: 400px;
				border-radius: 18px;
			    background: #fff;
			}
			
			*{
				padding: 0;
				margin: 0;
				list-style: none;
			}
			body{
				-webkit-text-size-adjust: 100% !important;
				overflow: hidden;
				height: 100%;
			}
			#he{
				/* width: 100%; */
				display: flex;/*弹性盒模型*/
				justify-content: center;/*主轴方向居中显示*/
				align-items: center;/*交叉轴方向居中显示*/
				height: 100vh;
				background-color: #cccccc54;
				flex-direction: column;
			}
			ul{
				height: 200px;
			}
			li{
				float: left;
				width: 10px;
				height: 20px;
				border-radius: 20px;
				margin-right: 10px;
			}
			li:nth-child(1){
				background-color: #f62e74;
				animation: love1 4s infinite;
			}
			li:nth-child(2){
				background-color: #f45330;
				animation: love2 4s infinite;
				animation-delay: 0.15s;
			}
			li:nth-child(3){
				background-color: #ffc883;
				animation: love3 4s infinite;
				animation-delay: 0.3s;
			}
			li:nth-child(4){
				background-color: #30d268;
				animation: love4 4s infinite;
				animation-delay: 0.45s;
			}
			li:nth-child(5){
				background-color: #006cb4;
				animation: love5 4s infinite;
				animation-delay: 0.6s;
			}
			li:nth-child(6){
				background-color: #784697;
				animation: love4 4s infinite;
				animation-delay: 0.75s;
			}
			li:nth-child(7){
				background-color: #ffc883;
				animation: love3 4s infinite;
				animation-delay: 0.9s;
			}
			li:nth-child(8){
				background-color: #f45330;
				animation: love2 4s infinite;
				animation-delay: 1.05s;
			}
			li:nth-child(9){
				background-color: #f62e74;
				animation: love1 4s infinite;
				animation-delay: 1.2s;
			}
			@keyframes love1{
				30%,50%{height: 30px; transform: translateY(-30px);}
				75%,100%{height: 20px; transform: translateY(0);}
			}
			@keyframes love2{
				30%,50%{height: 75px; transform: translateY(-62.5px);}
				75%,100%{height: 20px; transform: translateY(0);}
				
			}
			@keyframes love3{
				30%,50%{height: 100px; transform: translateY(-75px);}
				75%,100%{height: 20px; transform: translateY(0);}
			}
			@keyframes love4{
				30%,50%{height: 110px; transform: translateY(-60px);}
				75%,100%{height: 20px; transform: translateY(0);}
			}
			@keyframes love5{
				30%,50%{height: 120px; transform: translateY(-45px);}
				75%,100%{height: 20px; transform: translateY(0);}
			}
/* The switch - the box around the slider */
			.switch {
			  position: relative;
			  display: inline-block;
			  width: 45px;
			  height: 24px;
			}
			 
			/* Hide default HTML checkbox */
			.switch input {
			  opacity: 0;
			  width: 0;
			  height: 0;
			}

			/* The slider */
			.slider {
			  position: absolute;
			  cursor: pointer;
			  top: 0;
			  left: 0;
			  right: 0;
			  bottom: 0;
			  background-color: #ccc;
			  -webkit-transition: .4s;
			  transition: .4s;
			}
			 
			.slider:before {
			  position: absolute;
			  content: "";
			  height: 20px;
			  width: 20px;
			  left: 2px;
			  bottom: 2px;
			  background-color: white;
			  -webkit-transition: .4s;
			  transition: .4s;
			}
			 
			input:checked + .slider {
			  background-color: #00e600;
			}
			 
			input:checked + .slider:before {
			  -webkit-transform: translateX(21px);
			  -ms-transform: translateX(21px);
			  transform: translateX(21px);
			}
			 
			/* Rounded sliders */
			.slider.round {
			  border-radius: 34px;
			}
			 
			.slider.round:before {
			  border-radius: 50%;
			}

			/* 模态对话框动画效果 */
			@keyframes shake {
				0%, 100% { transform: translateX(0); }
				25% { transform: translateX(-5px); }
				75% { transform: translateX(5px); }
			}

			/* 模态对话框样式增强 */
			#hyts {
				backdrop-filter: blur(2px);
				-webkit-backdrop-filter: blur(2px);
			}

			/* 支付页面套餐选择样式 */
			.package-item:hover {
				border-color: #4CAF50 !important;
				box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
			}

			.package-item.selected {
				border-color: #4CAF50 !important;
				background-color: #f8fff8;
				box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
			}

			/* 支付方式样式 */
			.payment-method:hover {
				border-color: #1677FF !important;
				background-color: #f0f8ff;
			}

			.payment-method.selected {
				border-color: #1677FF !important;
				background-color: #f0f8ff;
			}

			.payment-method.selected .payment-check {
				border-color: #1677FF !important;
				background-color: #1677FF !important;
			}

			.payment-method.selected .payment-check span {
				display: inline !important;
			}

			/* 支付按钮悬停效果 */
			#payBtn:hover {
				opacity: 0.9;
				transform: translateY(-1px);
			}

			/* 确保支付页面正确显示 */
			#ts7 {
				z-index: 10000;
			}

			/* 支付页面内容样式 */
			#ts7 .package-item {
				transition: all 0.3s ease;
			}
		</style>
	</head>
	<body>
	
	<div class="div">
		
		<?php if($code==2){?>
				
		<div class="max" style="background-color: #fff;padding: 20px;border-radius: 15px;box-shadow: 0px 0px 20px -15px;margin: 20px 10px 10px;width: 100%;">
			<!-- <div style="text-align: center;">-->
			<!--	<img src="./img/a.gif" style="width: 50px;height: 50px;border: 2px solid seashell;border-radius: 100px;padding: 15px;"/>-->
			<!--</div> -->
		<div style="text-align: left;font-size: 13px;margin-top: 0px;color: #1277fa;">
                🎈公告：步数可同步微信、支付宝
			</div>
			<div style="text-align: left;font-size: 13px;margin-top: 8px;color: red;">
                伏笔工具箱 步数同步完全免费，支持iOS和安卓，欢迎推荐给好友！
			</div>
			 <marquee class="center-text" style="font-size: 13px;flex: 20; direction: up; scrollamount: 0;">
        新版本正式使用！<br>如有问题即可联系客服
        </marquee>
				<!-- <div style="font-size: 11px;font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;letter-spacing: 5px;background: #333;color: #ffaa00;padding: 8px 0px;width: 10rem;border-radius: 8px;font-weight: 700;text-align: center;margin-top: 8px;" id="xs">-->
				<!--	点我添加客服vx-->
				<!--</div> -->
		</div>
		<div class="max" style="background-color: #fff;padding: 20px;border-radius: 15px;box-shadow: 0px 0px 20px -15px;margin: 10px 10px 10px;width: 100%;">
		
		<!-- <div style="margin-top: 5px;font-weight: 700;font-size: 18px;letter-spacing: 3px;">登录</div>-->
		<!-- <div style="font-size: 10px;margin-top: 5px;color: slategray;">请登录 以获取独家热量值服务</div>-->
		
			<div class="max" style="display: flex;flex-direction: column;margin-top: 10px;">
		          
				<form id="myForm" action="/" onsubmit="return dosubmit()" method="post">
				
				<input   style="" id="user" placeholder="Zepp Life 账号"  value="" />
				
				<input   style="margin-top: 20px;" id="pass" placeholder="Zepp Life 密码"  value=""/>
				
				<input type="submit" style="margin-top: 20px;background: #5757ff;color: #fff;font-size: 12px;padding: 8px;" value="登录"/>
				
				</form>
				
				<div style="margin-top: 20px;font-size: 13px;font-weight: 700;display: flex;justify-content: space-between;">
				    <div style="color: #42c000;"><a href="https://bs.679l.cn/syff.html" style="margin-top: 18px;color: red;font-weight: 700;font-size: 13px;text-decoration: none;">不会使用? 查看教程</a></div>
				    <div id="lxkff" style="color: red;">联系客服</div>
			    </div>
				
			</div>
		            <a href="https://bs.679l.cn/fubi.php" target="_blank">
                <img src="https://679l.cn/huiyuan.png" alt="会员促销" style="max-width: 95%; height: auto; display: block; margin: 10px auto; border-radius: 3%;">
            </a>

		
		</div>
		
		<?php }else if($code==1){?>
	
		<div class="max" style="background-color: #fff;padding: 20px;border-radius: 15px;box-shadow: 0px 0px 20px -15px;margin: 10px 10px 10px;width: 100%;">
			
			
			<div style="display: flex;align-items: center;">
				<div><img src="https://p7.itc.cn/q_70/images03/20210209/241fb306e4254c72906f974c1afdb5af.jpeg" style="width: 60px;height: 60px;border-radius: 60px;"/></div>
				<div style="font-size: 15px;margin-left: 8px;margin-top: 5px;flex: 1;">
					<div style="font-weight: 700;" id="zh"></div>
					<div style="font-size: 14px;margin-top: 5px;font-weight: 700;color: slategray;" id="sc">上次执行 0</div>
					<?php if($vip == 1): ?>
					<div style="font-size: 12px;margin-top: 3px;color: #ff6600;display: flex;align-items: center;gap: 8px;">
						<span>会员到期: <?php echo $res['vip']; ?></span>
						<button id="renewVipBtn" style="
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
							color: white;
							border: none;
							padding: 4px 8px;
							border-radius: 4px;
							font-size: 10px;
							cursor: pointer;
							font-weight: bold;
							transition: transform 0.2s;
						" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
							续费
						</button>
					</div>
					<?php endif; ?>
				</div>
				<div id="tcdl" style="font-size: 12px;color: red;">
					[退出/切换]
				</div>
			</div>
			
			<div style="margin-top: 10px;color: #ffaa00;font-weight: 700;display: flex;align-items: center;">
				<div style="font-size: 11px;flex: 1;">开启自动同步 系统每天自动修改 无需来这执行啦!</div>
				<label class="switch">
				  <input type="checkbox" id="toggle" />
				  <span class="slider round"></span>
				</label>
				</div>
			
		<marquee class="center-text" style="font-size: 13px;flex: 20; direction: up; scrollamount: 0;color: red;">
        新版本正式使用！<br>如有问题即可联系客服
        </marquee>
			
			<div style="display: flex;flex-direction: column;margin-top: 58px;">
		          
				<form id="myForm2" action="/" onsubmit="return dosubmit2()" method="post">
				
				<input  type="number" maxlength="5"  style="" id="rlz" placeholder="热量值"  value="" />
				
				<input type="submit" style="margin-top: 30px;background: #5757ff;color: #fff;font-size: 12px;padding: 10px;" value="执行"/>
				
				</form>
				
			</div>

			<div style="margin-top: 20px;font-size: 13px;font-weight: 700;display: flex;justify-content: space-between;">
				<div id="fx" style="color: #42c000;">分享给好友使用[独乐乐不如众乐乐]</div>
				<div id="lxkf" style="color: red;">联系客服</div>
			</div>
		<a href="https://bs.679l.cn/fubi.php" target="_blank">
                <img src="https://679l.cn/huiyuan.png" alt="会员促销" style="max-width: 95%; height: auto; display: block; margin: 10px auto; border-radius: 3%;">
            </a>
		
			<!--<div style="text-align: center;">-->
			<!--	<a href="https://haokawx.lot-ml.com/h5order/index?pudID=465&userid=14076"><img src="http://bs.679l.cn/llk.png" style="width: 80%;margin-top: 20px;border-radius: 10px;"/></a>-->
			<!--</div>-->
		
		
		</div>
		
		<?php }?>

	</div>

	<!-- 备案号 - 固定在页面底部 -->
	<div style="position: fixed; bottom: 10px; width: 100%; text-align: center; z-index: 1;">
		<a href="https://beian.miit.gov.cn/" style="color: #666; font-size: 12px; text-decoration: none;">ICP备案号：陇ICP备2023000198号-1</a>
	</div>
	

	<div id="jz" style="position: fixed; width: 100%;height: 100%;top: 0;z-index: 99999;display: none;">
		<div id="he">
			<ul>
				<li></li>
				<li></li>
				<li></li>
				<li></li>
				<li></li>
				<li></li>
				<li></li>
				<li></li>
				<li></li>
			</ul>
			<div style="margin-top:-100px;font-size:15px;">加载中</div>
		</div>
	</div>


	<div style="width: 100%;height: 100vh;background-color: #00000042;position:fixed;top: 0;left: 0;z-index: 9999;align-items: center;justify-content: center;display: none;" id="hyts">

		<div id="ts1" style="max-width: 32rem;margin-top: -10%;width: 100%;display: none;">
			
			<div style="background-color: #fff;padding: 15px;margin: 0px 30px;border-radius: 12px;font-size: 13px;text-align: center;">
				<div style="text-align: end;margin-bottom: -25px;">
					<img id="gbhyts1" src="https://jz.jzapi.top/wenjian/icon/gb.gif" style="width: 30px;height: 30px;" />
				</div>
				<div style="font-size: 14px;font-weight: 700;margin-bottom: 10px;">关 于</div>
				<div style="color: red;margin-bottom: 10px;">开通会员可开启自动同步o</div>
				<div style="text-align: left;color: slategrey;">自动同步说明</div>
				<div style="text-align: left;margin-top: 6px;color: slategrey;display: flex;justify-content: space-between;">
					<div>开启后 设置一个最终值  如52100</div>  <div style="color: red;margin-left: 6px;">[以后不用管了]</div>
				</div>
				<div style="text-align: left;margin-top: 6px;color: slategrey;display: flex;justify-content: space-between;">
					<div>每天9点-21点内 每小时会更新1次步数</div>  <div style="color: red;margin-left: 6px;">[系统全自动]</div>
				</div>
				<div style="text-align: left;margin-top: 6px;color: slategrey;display: flex;justify-content: space-between;">
					<div>↑步数每小时会慢慢增加一点</div> <div style="color: red;margin-left: 6px;">[仿真 安全可靠]</div>
				</div>
				<div style="text-align: left;margin-top: 6px;color: slategrey;display: flex;justify-content: space-between;">
					<div>↑每日最后一次是最终值上下3000左右</div><div style="color: red;margin-left: 6px;">[效果逼真]</div>
				</div>
				<div style="color: red;margin-top: 20px;font-weight: 700;color: #ffaa00;">若以上说明还不太理解 可联系客服帮忙</div>
				<div style="display: flex;margin: 20px 0px 5px;justify-content: space-around;">
					
					<div id="gbhyts" style="background-color: #eeeeee;flex: 1;padding: 6px 0px;border-radius: 8px;margin-right: 15px;font-weight: 700;">自助开通</div>

					<div id="lxkfff" style="background-color: #000;flex: 1;padding: 6px 0px;border-radius: 8px;color: #fff;font-weight: 700;">联系客服</div>
					
				</div>
				<div style="text-align: left;margin-bottom: 5px;color: slategray;display: flex;justify-content: space-between;">
					<div>↑自助开通会员服务</div>
					<div>需要帮助请联系客服↑</div>
				</div>
				
			</div>
			
		</div>
		
		<div id="ts2" style="max-width: 32rem;margin-top: -10%;width: 100%;display: none;">
			
			<div style="background-color: #fff;padding: 15px;margin: 0px 30px;border-radius: 12px;font-size: 13px;text-align: center;">
				<div style="text-align: end;margin-bottom: -25px;">
					<img id="gbhyts2" src="https://jz.jzapi.top/wenjian/icon/gb.gif" style="width: 30px;height: 30px;" />
				</div>
				<div style="font-size: 14px;font-weight: 700;margin-bottom: 30px;">提 示</div>
				<form id="myForm3" action="/" onsubmit="return dosubmit3()" method="post">
				<input type="number"  style="font-size: 13px;border-radius: 8px;" maxlength="5" id="ydrlz" placeholder="请输入最终值"  value="" />
				<div style="font-size: 13px;color: slategray;">↑建议5w左右 如最终值为50000<br>那么每天最后的步数为47000-53000之间</div>
				<div style="font-size: 13px;color: slategray;">每天9-21点内 系统每小时自动同步一次 慢慢增加效果逼真</div>
				<input type="submit" style="margin-top: 30px;background: #5757ff;color: #fff;font-size: 12px;padding: 10px;" value="保存开启自动同步"/>
				</form>
			<div>
			
		<div>
		</div></div></div></div>
		<div id="ts3" style="max-width: 32rem;margin-top: -10%;width: 100%;display: none;">
			
			<div style="background-color: #fff;padding: 15px;margin: 0px 30px;border-radius: 12px;font-size: 13px;text-align: center;">
				<div style="text-align: end;margin-bottom: -25px;">
					<img id="gbhyts3" src="https://jz.jzapi.top/wenjian/icon/gb.gif" style="width: 30px;height: 30px;" />
				</div>
				<div id="bts" style="font-size: 14px;font-weight: 700;margin-bottom: 10px;">联系客服</div>
				<img src="https://bs.679l.cn/8.jpg" style="width: 50%;" alt="" />
				<div style="margin: 10px 0px 5px;color: red;">长按识别上方二维码  添加客服</div>
			<div>
			
		<div>
		</div></div></div></div>
			<div id="ts4" style="max-width: 32rem;margin-top: -10%;width: 100%;display: none;">
				
				<div style="background-color: #fff;padding: 15px;margin: 0px 30px;border-radius: 12px;font-size: 13px;text-align: center;">
					<div style="text-align: end;margin-bottom: -25px;">
						<img id="gbhyts4" src="https://jz.jzapi.top/wenjian/icon/gb.gif" style="width: 30px;height: 30px;" />
					</div>
					<div style="font-size: 14px;font-weight: 700;margin-bottom: 10px;">分享好友</div>
					<img src="https://bs.679l.cn/qfx.png" style="width: 70%;" alt="" />
					<div style="margin: 12px 0px 5px;color: red;font-weight: 700;">长按上方图片 转发好友或保存发送</div>
				<div>
				
			<div>
		</div></div></div></div>
			<div id="ts5" style="max-width: 32rem;margin-top: -10%;width: 100%;display: none;">
				<div style="background-color: #fff;padding: 15px;margin: 0px 30px;border-radius: 12px;font-size: 13px;text-align: center;">
					
					<div style="font-size: 14px;font-weight: 700;margin-bottom: 30px;">首次使用请先绑定小程序</div>
					<div style="color: red;">1.截图识别下方小程序码进入</div>

        <img src="https://bs.679l.cn/fubi.jpg" alt="小程序码" style="max-width: 50%; height: auto;"/>
        <div style="color: red;">2. 点击图片所示ID即可复制成功</div>

        <img src="https://679l.cn/fubi2.png" alt="ID图片" style="max-width: 50%; height: auto;"/>
					<form id="myForm5" action="/" onsubmit="return dosubmit5()" method="post">
					<input type="text"  style="font-size: 13px;border-radius: 8px;margin-top:10px;" id="xcxid" placeholder="请输入小程序id"  value="" />
					
					<div style="display:flex;">
					    <input type="submit" style="margin-top: 10px;background: #3a3a3a;color: #fff;font-size: 12px;padding: 10px;" value="换绑"/>
					    <input type="submit" style="margin-top: 10px;background: #5757ff;color: #fff;font-size: 12px;padding: 10px;" value="绑定"/>
					</div>
					
					<div style="display: flex;justify-content: space-between;font-size: 11px;">
    <div style="color: #ea9000;">换绑粘贴id后点我即可</div>
    <div style="color: red;">首次使用请先绑定！</div>
    </div>
					
					</form>
				<div>
				
			<div>
		</div></div></div></div>
			<div id="ts6" style="max-width: 32rem;margin-top: -10%;width: 100%;display: none;">

			<div style="background-color: #fff;padding: 15px;margin: 0px 30px;border-radius: 12px;font-size: 13px;text-align: center;">
				<div style="text-align: end;margin-bottom: -25px;">
					<img id="gbhyts6" src="https://jz.jzapi.top/wenjian/icon/gb.gif" style="width: 30px;height: 30px;" />
				</div>
				<div id="bts" style="font-size: 14px;font-weight: 700;margin-bottom: 10px;">资源紧张 执行方法</div>
				<img src="https://bs.679l.cn/xcx.jpg" style="width: 30%;" alt="" />
				<div style="margin: 10px 0px 5px;color: red;">vx识别上方二维码<br>进去随机下载一个表情包获取免费时长</div>
				<div style="margin: 10px 0px 5px;color: red;">QQ:2682281633 VX:z12150301</div>
				<div style="margin: 10px 0px 5px;color: red;">每天操作嫌麻烦? 联系客服开通会员</div>
			</div>
		</div>

  		<!-- 支付页面对话框 -->
		<div id="ts7" style="max-width: 32rem;margin-top: -10%;width: 100%;display: none;">
			<div style="background-color: #fff;padding: 15px;margin: 0px 30px;border-radius: 12px;font-size: 13px;text-align: center;">
				<div style="text-align: end;margin-bottom: -25px;">
					<img id="gbhyts7" src="https://jz.jzapi.top/wenjian/icon/gb.gif" style="width: 30px;height: 30px;" />
				</div>

				<!-- 头部信息 -->
				<div style="background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);color: white;padding: 12px;border-radius: 8px;margin-bottom: 15px;margin-top: 25px;position: relative;">
					<div style="display: flex;align-items: center;justify-content: center;margin-bottom: 6px;">
						<div style="width: 24px;height: 24px;background: rgba(255,255,255,0.2);border-radius: 50%;display: flex;align-items: center;justify-content: center;margin-right: 8px;">💎</div>
						<div style="font-size: 14px;font-weight: bold;"><?php echo htmlspecialchars($user); ?></div>
					</div>
					<div style="font-size: 11px;opacity: 0.9;">👑 您正在激活zeep账号</div>
					<div style="font-size: 10px;opacity: 0.8;margin-top: 4px;text-align: center;">有任何问题 可点击联系客服</div>
					<div style="font-size: 10px;opacity: 0.8;text-align: center;">注意购买正确套餐激活账号 方便联系入口</div>
					<!-- 右侧装饰图标 -->
					<div style="position: absolute;right: 10px;top: 50%;transform: translateY(-50%);opacity: 0.3;font-size: 24px;">👤</div>
				</div>

				<!-- 套餐选择 -->
				<div style="margin-bottom: 15px;">
					<div style="font-size: 13px;font-weight: bold;margin-bottom: 10px;color: #333;">选择套餐</div>
					<div style="display: flex;justify-content: space-between;gap: 8px;">
						<!-- 1个月套餐 -->
						<div class="package-item" data-package="1" style="flex: 1;border: 2px solid #e0e0e0;border-radius: 6px;padding: 8px;cursor: pointer;transition: all 0.3s;">
							<div style="font-size: 11px;font-weight: bold;margin-bottom: 3px;">月</div>
							<div style="color: #ff6b6b;font-size: 13px;font-weight: bold;">￥9.9</div>
							<div style="font-size: 9px;color: #999;text-decoration: line-through;">原价￥19.9</div>
							<div style="font-size: 9px;color: #666;margin-top: 2px;">每天不到0.33元</div>
						</div>
						<!-- 3个月套餐 -->
						<div class="package-item" data-package="3" style="flex: 1;border: 2px solid #e0e0e0;border-radius: 6px;padding: 8px;cursor: pointer;transition: all 0.3s;position: relative;">
							<div style="position: absolute;top: -6px;right: -6px;background: #ff4444;color: white;font-size: 9px;padding: 1px 4px;border-radius: 8px;">推荐</div>
							<div style="font-size: 11px;font-weight: bold;margin-bottom: 3px;">季</div>
							<div style="color: #ff6b6b;font-size: 13px;font-weight: bold;">￥19.9</div>
							<div style="font-size: 9px;color: #999;text-decoration: line-through;">原价￥39.9</div>
							<div style="font-size: 9px;color: #666;margin-top: 2px;">每天不到0.22元</div>
						</div>
						<!-- 年套餐 -->
						<div class="package-item" data-package="year" style="flex: 1;border: 2px solid #e0e0e0;border-radius: 6px;padding: 8px;cursor: pointer;transition: all 0.3s;">
							<div style="font-size: 11px;font-weight: bold;margin-bottom: 3px;">年</div>
							<div style="color: #ff6b6b;font-size: 13px;font-weight: bold;">￥29.9</div>
							<div style="font-size: 9px;color: #999;text-decoration: line-through;">原价￥59.9</div>
							<div style="font-size: 9px;color: #666;margin-top: 2px;">每天不到0.08元</div>
						</div>
						<!-- 永久套餐 -->
						<div class="package-item" data-package="forever" style="flex: 1;border: 2px solid #e0e0e0;border-radius: 6px;padding: 8px;cursor: pointer;transition: all 0.3s;">
							<div style="font-size: 11px;font-weight: bold;margin-bottom: 3px;">永久</div>
							<div style="color: #ff6b6b;font-size: 13px;font-weight: bold;">￥39.9</div>
							<div style="font-size: 9px;color: #999;text-decoration: line-through;">原价￥79.9</div>
							<div style="font-size: 9px;color: #666;margin-top: 2px;">一次购买 终身使用</div>
						</div>
					</div>
				</div>

				<!-- 支付方式 -->
				<div style="margin-bottom: 15px;">
					<div style="font-size: 13px;font-weight: bold;margin-bottom: 10px;color: #333;">选择支付方式</div>
					<div class="payment-method" data-type="wxpay" style="display: flex;align-items: center;padding: 8px;border: 2px solid #e0e0e0;border-radius: 6px;margin-bottom: 8px;cursor: pointer;transition: all 0.3s;">
						<div style="width: 18px;height: 18px;background: #1AAD19;border-radius: 50%;display: flex;align-items: center;justify-content: center;margin-right: 10px;">
							<span style="color: white;font-size: 12px;">💬</span>
						</div>
						<span style="font-size: 13px;font-weight: 500;">微信支付</span>
						<div class="payment-check" style="margin-left: auto;width: 16px;height: 16px;border: 2px solid #ddd;border-radius: 50%;display: flex;align-items: center;justify-content: center;">
							<span style="color: #1AAD19;font-size: 10px;display: none;">✓</span>
						</div>
					</div>
					<div class="payment-method selected" data-type="alipay" style="display: flex;align-items: center;padding: 8px;border: 2px solid #1677FF;border-radius: 6px;cursor: pointer;transition: all 0.3s;background-color: #f0f8ff;">
						<div style="width: 18px;height: 18px;background: #1677FF;border-radius: 50%;display: flex;align-items: center;justify-content: center;margin-right: 10px;">
							<span style="color: white;font-size: 12px;">💳</span>
						</div>
						<span style="font-size: 13px;font-weight: 500;">支付宝支付</span>
						<div class="payment-check" style="margin-left: auto;width: 16px;height: 16px;border: 2px solid #1677FF;border-radius: 50%;display: flex;align-items: center;justify-content: center;background: #1677FF;">
							<span style="color: white;font-size: 10px;">✓</span>
						</div>
					</div>
				</div>

				<!-- 立即购买按钮 -->
				<button id="payBtn" style="width: 100%;background: #5757ff;color: white;border: none;padding: 10px;border-radius: 8px;font-size: 12px;font-weight: bold;cursor: pointer;margin-bottom: 8px;">
					立即购买
				</button>

				<!-- 底部提示 -->
				<div style="font-size: 10px;color: #999;line-height: 1.3;">
					<span style="color: #4CAF50;">●</span> 购买即代表同意<span style="color: #1677FF;">《用户协议》</span>
					<span style="color: #ff4444;">●</span> 不支持退款
				</div>
			</div>
		</div>

   
		<!--<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>-->
		<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
		<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.0.0/crypto-js.min.js"></script>
		<script type="text/javascript">
			
			var gbhyts = document.getElementById('gbhyts');
			var gbhyts1 = document.getElementById('gbhyts1');
			var gbhyts2 = document.getElementById('gbhyts2');
			var gbhyts3 = document.getElementById('gbhyts3');
			var gbhyts4 = document.getElementById('gbhyts4');
			var gbhyts6 = document.getElementById('gbhyts6');
			var gbhyts7 = document.getElementById('gbhyts7');
			
			
			// 移除背景点击关闭功能，只保留关闭按钮
			// 自助开通按钮点击事件 - 显示支付页面
			gbhyts.addEventListener('click', function() {
				showModal("ts7"); // 直接显示支付页面，showModal会自动隐藏其他对话框
			});
			gbhyts1.addEventListener('click', function() {gb()});
			gbhyts2.addEventListener('click', function() {gb()});
			gbhyts3.addEventListener('click', function() {gb()});
			gbhyts4.addEventListener('click', function() {gb()});
			gbhyts6.addEventListener('click', function() {gb()});
			gbhyts7.addEventListener('click', function() {gb()});

			// 添加模态对话框背景点击事件处理，阻止点击背景关闭
			document.getElementById('hyts').addEventListener('click', function(e) {
				// 如果点击的是背景层本身，不关闭对话框
				if (e.target === this) {
					e.stopPropagation();
					// 可以添加一个轻微的动画效果提示用户
					var activeDialog = document.querySelector('#hyts > div[style*="display: block"], #hyts > div[style*="display: "]');
					if (activeDialog) {
						activeDialog.style.animation = 'shake 0.3s ease-in-out';
						setTimeout(function() {
							activeDialog.style.animation = '';
						}, 300);
					}
				}
			});

			// 阻止对话框内容区域的点击事件冒泡
			var dialogContents = document.querySelectorAll('#ts1 > div, #ts2 > div, #ts3 > div, #ts4 > div, #ts5 > div, #ts6 > div');
			dialogContents.forEach(function(content) {
				content.addEventListener('click', function(e) {
					e.stopPropagation();
				});
			});

			// 阻止ESC键关闭模态对话框
			document.addEventListener('keydown', function(e) {
				var hyts = document.getElementById('hyts');
				if (e.key === 'Escape' && hyts.style.display === 'flex') {
					e.preventDefault();
					e.stopPropagation();
					// 添加震动效果提示用户必须通过关闭按钮关闭
					var activeDialog = document.querySelector('#hyts > div[style*="display: block"], #hyts > div[style*="display: "]');
					if (activeDialog) {
						activeDialog.style.animation = 'shake 0.3s ease-in-out';
						setTimeout(function() {
							activeDialog.style.animation = '';
						}, 300);
					}
				}
			});
			
			
			
			function gb(){
				var hyts = document.getElementById("hyts");
				var ts1 = document.getElementById("ts1");
				var ts2 = document.getElementById("ts2");
				var ts3 = document.getElementById("ts3");
				var ts4 = document.getElementById("ts4");
				var ts5 = document.getElementById("ts5");
				var ts6 = document.getElementById("ts6");
				var ts7 = document.getElementById("ts7");

				// 关闭模态对话框
				hyts.style.display = 'none';
				ts1.style.display = 'none';
				ts2.style.display = 'none';
				ts3.style.display = 'none';
				ts4.style.display = 'none';
				ts5.style.display = 'none';
				ts6.style.display = 'none';
				ts7.style.display = 'none';

				// 恢复页面滚动（如果之前被禁用）
				document.body.style.overflow = '';
			}

			// 显示模态对话框的函数
			function showModal(dialogId) {
				var hyts = document.getElementById("hyts");
				var dialog = document.getElementById(dialogId);

				// 先隐藏所有对话框
				var allDialogs = ['ts1', 'ts2', 'ts3', 'ts4', 'ts5', 'ts6', 'ts7'];
				allDialogs.forEach(function(id) {
					var d = document.getElementById(id);
					if (d) d.style.display = 'none';
				});

				// 禁用页面滚动
				document.body.style.overflow = 'hidden';

				// 显示模态对话框
				hyts.style.display = 'flex';
				dialog.style.display = '';

				// 如果是支付页面，初始化支付页面功能
				if (dialogId === 'ts7') {
					initPaymentPage();
				}
			}

			// 初始化支付页面功能
			function initPaymentPage() {
				var selectedPackage = '3'; // 默认选择季套餐
				var selectedPayment = 'alipay'; // 默认选择支付宝
				var packagePrices = {
					'1': '0.01',
					'3': '19.9',
					'year': '29.9',
					'forever': '39.9'
				};

				// 套餐选择功能
				var packageItems = document.querySelectorAll('.package-item');
				packageItems.forEach(function(item) {
					// 移除之前的事件监听器
					item.removeEventListener('click', packageClickHandler);
					// 添加新的事件监听器
					item.addEventListener('click', packageClickHandler);
				});

				// 支付方式选择功能
				var paymentMethods = document.querySelectorAll('.payment-method');
				paymentMethods.forEach(function(method) {
					method.addEventListener('click', function() {
						// 移除所有选中状态
						paymentMethods.forEach(function(m) {
							m.classList.remove('selected');
							m.style.borderColor = '#e0e0e0';
							m.style.backgroundColor = 'transparent';
							var check = m.querySelector('.payment-check');
							check.style.borderColor = '#ddd';
							check.style.backgroundColor = 'transparent';
							var checkIcon = check.querySelector('span');
							checkIcon.style.display = 'none';
						});

						// 添加选中状态
						this.classList.add('selected');
						this.style.borderColor = '#1677FF';
						this.style.backgroundColor = '#f0f8ff';
						var check = this.querySelector('.payment-check');
						check.style.borderColor = '#1677FF';
						check.style.backgroundColor = '#1677FF';
						var checkIcon = check.querySelector('span');
						checkIcon.style.display = 'inline';
						checkIcon.style.color = 'white';

						// 更新选中的支付方式
						selectedPayment = this.getAttribute('data-type');
					});
				});

				function packageClickHandler() {
					// 移除所有选中状态
					packageItems.forEach(function(pkg) {
						pkg.classList.remove('selected');
					});
					// 添加选中状态
					this.classList.add('selected');
					selectedPackage = this.getAttribute('data-package');

					// 更新支付按钮文本
					var payBtn = document.getElementById('payBtn');
					payBtn.textContent = '立即购买 - ￥' + packagePrices[selectedPackage];
				}

				// 默认选择季套餐
				var defaultPackage = document.querySelector('.package-item[data-package="3"]');
				if (defaultPackage) {
					defaultPackage.classList.add('selected');
					var payBtn = document.getElementById('payBtn');
					payBtn.textContent = '立即购买 - ￥' + packagePrices['3'];
				}

				// 支付按钮点击事件
				var payBtn = document.getElementById('payBtn');
				payBtn.removeEventListener('click', payClickHandler);
				payBtn.addEventListener('click', payClickHandler);

				function payClickHandler() {
					// 检查是否选择了支付方式
					if (!selectedPayment) {
						swal("请选择支付方式", "请先选择微信支付或支付宝支付", "warning");
						return;
					}

					// 获取支付方式名称
					var paymentName = selectedPayment === 'wxpay' ? '微信支付' : '支付宝支付';

					// 支付确认对话框
					swal({
						title: "支付确认",
						text: "确定购买" + getPackageName(selectedPackage) + "套餐吗？\n价格：￥" + packagePrices[selectedPackage] + "\n支付方式：" + paymentName,
						type: 'info',
						showCancelButton: true,
						confirmButtonColor: "#667eea",
						confirmButtonText: "确定支付",
						cancelButtonText: "取消",
						closeOnConfirm: false
					}, function (isConfirm) {
						if (isConfirm) {
							// 显示支付处理中
							swal({
								title: "处理中...",
								text: "正在处理您的支付请求",
								type: "info",
								showConfirmButton: false,
								allowOutsideClick: false
							});

							// 发送AJAX请求处理支付
							var xhr = new XMLHttpRequest();
							xhr.open('POST', 'process_payment.php', true); // 使用正式版本
							xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
							xhr.timeout = 30000; // 30秒超时

							xhr.ontimeout = function() {
								swal.close();
								swal("请求超时", "支付请求超时，请重试", "error");
							};

							xhr.onreadystatechange = function() {
								console.log('readyState:', xhr.readyState, 'status:', xhr.status);

								if (xhr.readyState === 4) {
									console.log('最终响应状态:', xhr.status);
									console.log('响应内容:', xhr.responseText);
									console.log('响应头:', xhr.getAllResponseHeaders());

									// 关闭处理中对话框
									swal.close();

									if (xhr.status === 200) {
										try {
											var response = JSON.parse(xhr.responseText);
											console.log('解析后的响应:', response);

											// 关闭处理中对话框
											swal.close();

											if (response.success) {
												console.log('支付成功，处理支付结果:', response);

												// 处理支付方式 - 跳转到自定义支付页面
												console.log('支付订单创建成功，订单号:', response.trade_no);
												console.log('调用 showQRCodePayment 函数...');
												try {
													// 传递更多参数到支付页面
													showQRCodePayment(response.qrcode, response.trade_no, response.pay_type, response.money);
												} catch (e) {
													console.error('showQRCodePayment 函数调用失败:', e);
													// 备用方案：直接跳转到易支付页面
													var paymentUrl = 'https://mpay.5xv.cn/pay/' + response.trade_no;
													swal({
														title: "支付订单已创建",
														text: "订单号: " + response.trade_no + "\n\n点击确定跳转到支付页面",
														type: "info",
														showConfirmButton: true,
														confirmButtonText: "立即支付"
													}, function() {
														window.open(paymentUrl, '_blank');
													});
												}
											} else {
												swal("支付失败", response.message || "支付处理失败，请重试", "error");
											}
										} catch (e) {
											console.error('JSON解析错误:', e);
											console.error('原始响应:', xhr.responseText);
											swal("支付失败", "服务器响应异常: " + xhr.responseText, "error");
										}
									} else {
										swal("支付失败", "网络错误 (状态码: " + xhr.status + ")", "error");
									}
								}
							};

							// 发送支付数据（使用用户选择的支付方式）
							var postData = 'package=' + encodeURIComponent(selectedPackage) +
										   '&price=' + encodeURIComponent(packagePrices[selectedPackage]) +
										   '&pay_type=' + encodeURIComponent(selectedPayment);
							xhr.send(postData);
						}
					});
				}

				function getPackageName(packageType) {
					switch(packageType) {
						case '1': return '月';
						case '3': return '季';
						case 'year': return '年';
						case 'forever': return '永久';
						default: return '季';
					}
				}

				// 显示二维码支付
				function showQRCodePayment(qrcode, tradeNo, payType, amount) {
					console.log('showQRCodePayment 被调用:', qrcode, tradeNo, payType, amount);

					// 构建易支付页面链接
					var paymentUrl = 'https://mpay.5xv.cn/pay/' + tradeNo;
					console.log('支付页面链接:', paymentUrl);

					// 在模态框中显示支付链接
					showPaymentModal(paymentUrl, tradeNo, amount);
				}

				// 显示支付模态框
				function showPaymentModal(paymentUrl, tradeNo, amount) {
					// 创建模态框容器
					var modal = document.createElement('div');
					modal.id = 'paymentModal';
					modal.style.cssText = `
						position: fixed;
						top: 0;
						left: 0;
						width: 100%;
						height: 100%;
						background: rgba(0,0,0,0.8);
						z-index: 10000;
						display: flex;
						align-items: center;
						justify-content: center;
						padding: 20px;
						box-sizing: border-box;
					`;

					// 创建模态框内容
					var modalContent = document.createElement('div');
					modalContent.style.cssText = `
						background: white;
						border-radius: 15px;
						width: 100%;
						max-width: 800px;
						height: 90%;
						max-height: 700px;
						position: relative;
						box-shadow: 0 20px 40px rgba(0,0,0,0.3);
						display: flex;
						flex-direction: column;
					`;

					// 创建头部
					var header = document.createElement('div');
					header.style.cssText = `
						padding: 20px;
						border-bottom: 1px solid #eee;
						display: flex;
						justify-content: space-between;
						align-items: center;
						background: #f8f9fa;
						border-radius: 15px 15px 0 0;
					`;

					var headerInfo = document.createElement('div');
					headerInfo.innerHTML = `
						<h3 style="margin: 0; color: #333;">支付订单</h3>
						<p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">订单号: ${tradeNo} | 金额: ￥${amount}</p>
					`;

					var closeBtn = document.createElement('button');
					closeBtn.innerHTML = '×';
					closeBtn.style.cssText = `
						background: none;
						border: none;
						font-size: 24px;
						cursor: pointer;
						color: #999;
						padding: 5px;
						line-height: 1;
					`;
					closeBtn.onclick = closePaymentModal;

					header.appendChild(headerInfo);
					header.appendChild(closeBtn);

					// 创建iframe
					var iframe = document.createElement('iframe');
					iframe.src = paymentUrl;
					iframe.style.cssText = `
						flex: 1;
						border: none;
						border-radius: 0 0 15px 15px;
					`;
					iframe.frameBorder = '0';

					// 监听iframe的URL变化
					iframe.onload = function() {
						try {
							var iframeUrl = iframe.contentWindow.location.href;
							console.log('iframe URL变化:', iframeUrl);

							// 如果跳转到了返回页面（包含我们的域名），说明支付完成
							if (iframeUrl.includes(window.location.hostname) ||
								iframeUrl.includes('return_url') ||
								iframeUrl.includes('success')) {
								console.log('检测到支付完成，关闭模态框');

								// 停止支付检测
								if (window.paymentCheckInterval) {
									clearInterval(window.paymentCheckInterval);
									window.paymentCheckInterval = null;
								}

								// 关闭模态框
								closePaymentModal();

								// 显示成功提示
								swal({
									title: "支付成功！",
									text: "会员权限已开通，页面即将刷新",
									type: "success",
									timer: 2000,
									showConfirmButton: false
								});

								// 2秒后刷新页面
								setTimeout(function() {
									window.location.reload();
								}, 2000);
							}
						} catch (e) {
							// 跨域限制，无法访问iframe内容，这是正常的
							console.log('无法访问iframe内容（跨域限制）');
						}
					};

					// 组装模态框
					modalContent.appendChild(header);
					modalContent.appendChild(iframe);
					modal.appendChild(modalContent);

					// 添加到页面
					document.body.appendChild(modal);

					// 防止背景滚动
					document.body.style.overflow = 'hidden';

					// 点击背景关闭
					modal.addEventListener('click', function(e) {
						if (e.target === modal) {
							closePaymentModal();
						}
					});

					// 开始检测支付状态
					startPaymentCheck(tradeNo);
				}



				// 关闭支付模态框
				function closePaymentModal() {
					// 停止支付检测
					if (window.paymentCheckInterval) {
						clearInterval(window.paymentCheckInterval);
						window.paymentCheckInterval = null;
					}

					var modal = document.getElementById('paymentModal');
					if (modal) {
						modal.remove();
						document.body.style.overflow = '';
					}
				}

				// 开始支付状态检测
				function startPaymentCheck(tradeNo) {
					console.log('开始检测支付状态:', tradeNo);

					// 每2秒检查一次，更频繁的检测
					window.paymentCheckInterval = setInterval(function() {
						checkPayment(tradeNo);
					}, 2000);

					// 10分钟后停止检查
					setTimeout(function() {
						if (window.paymentCheckInterval) {
							clearInterval(window.paymentCheckInterval);
							window.paymentCheckInterval = null;
							console.log('支付检测超时停止');
						}
					}, 10 * 60 * 1000);
				}

				// 检查支付状态
				function checkPayment(tradeNo) {
					var xhr = new XMLHttpRequest();
					xhr.open('GET', 'check_payment_status.php?trade_no=' + encodeURIComponent(tradeNo), true);
					xhr.onreadystatechange = function() {
						if (xhr.readyState === 4 && xhr.status === 200) {
							try {
								var response = JSON.parse(xhr.responseText);
								console.log('支付状态:', response);

								if (response.success && response.paid) {
									// 支付成功，立即停止检测并关闭模态框
									console.log('支付成功，立即关闭模态框');

									// 停止支付检测
									if (window.paymentCheckInterval) {
										clearInterval(window.paymentCheckInterval);
										window.paymentCheckInterval = null;
									}

									// 立即关闭模态框
									closePaymentModal();

									// 显示成功提示
									swal({
										title: "支付成功！",
										text: "会员权限已开通，页面即将刷新",
										type: "success",
										timer: 1500,
										showConfirmButton: false
									});

									// 1.5秒后刷新页面
									setTimeout(function() {
										window.location.reload();
									}, 1500);
								}
							} catch (e) {
								console.error('解析支付状态失败:', e);
							}
						}
					};
					xhr.send();
				}




				// 检查支付状态
				function checkPaymentStatus(tradeNo) {
					swal({
						title: "检查支付状态...",
						text: "正在确认您的支付状态",
						type: "info",
						showConfirmButton: false,
						allowOutsideClick: false
					});

					// 发送AJAX请求检查支付状态
					var xhr = new XMLHttpRequest();
					xhr.open('GET', 'check_payment_status.php?trade_no=' + encodeURIComponent(tradeNo), true);

					xhr.onreadystatechange = function() {
						if (xhr.readyState === 4) {
							if (xhr.status === 200) {
								try {
									var response = JSON.parse(xhr.responseText);
									if (response.success && response.paid) {
										// 支付成功
										swal("支付成功!", "会员服务已开通，感谢您的支持！", "success");
										setTimeout(function() {
											gb(); // 关闭支付页面
											// 支付成功后不自动显示设置提示框，让用户手动开启
										}, 2000);
									} else {
										// 支付未完成
										swal({
											title: "支付未完成",
											text: "请完成支付后再次点击确认",
											type: "warning",
											showConfirmButton: true,
											confirmButtonText: "重新检查",
											showCancelButton: true,
											cancelButtonText: "取消"
										}, function(isConfirm) {
											if (isConfirm) {
												// 重新检查
												checkPaymentStatus(tradeNo);
											}
										});
									}
								} catch (e) {
									swal("检查失败", "状态检查异常，请刷新页面重试", "error");
								}
							} else {
								swal("检查失败", "网络错误，请重试", "error");
							}
						}
					};

					xhr.send();
				}
			}
			
			if(<?php echo $code;?>==1){
				
				var code = <?php echo $zdsb;?>;
				var vip = <?php echo $vip;?>;
				var xcx = <?php echo $xcx;?>;
				
				
				if(xcx!=1){
					showModal("ts5");
				}

				if(<?php echo $ggsc;?>==1){
				    showModal("ts6");
				}
				    
				
				
				if(vip==1){
					// VIP用户根据数据库中的code值设置开关状态
					if(code==1){
						document.getElementById('toggle').checked = true
					}else{
						document.getElementById('toggle').checked = false
					}
				}else{
					document.getElementById('toggle').checked = false
				}
				
				document.getElementById('toggle').addEventListener('change', function() {
					
				  
				  if(vip==1){
					  if(this.checked){
						  this.checked = !this.checked
						  showModal("ts2");
					  }else{
						  var form = document.createElement('form');
						  form.action = '/';
						  form.method = 'POST';
						  var input = document.createElement('input');
						  input.type = 'hidden';
						  input.name = 'data'; // 这是你发送到服务器的键
						  var arr = {lx:'ydgb',sj:sj()}
						  input.value = jm(JSON.stringify(arr));
						   
						  // 将input添加到form
						  form.appendChild(input);
						   
						  // 将form添加到body，以便可以提交
						  document.body.appendChild(form);
						   
						  // 提交表单
						  form.submit();
					  }
				  }else{
					  this.checked = !this.checked
					  showModal("ts1");
				  }
				  
				});
				
				var sc = getCookie('sc')
				if(sc){
					document.getElementById("sc").innerHTML = '上次执行 '+sc
				}
				document.getElementById("zh").innerHTML = "<?php echo $user?>"
				var tcdl = document.getElementById('tcdl');
				tcdl.addEventListener('click', function() {
										swal({
											title: "",
											text: "确定退出当前账号吗？",
											type: 'warning',
											showCancelButton: true,
											confirmButtonColor: "#DD6B55",
											confirmButtonText: "确定",
											cancelButtonText: "取消",
											closeOnConfirm: false
											},
											function (isConfirm) {
												if(isConfirm){
													var cookies = document.cookie.split(";"); // 将所有cookie分割成一个数组
													for (var i = 0; i < cookies.length; i++) {
													  var cookie = cookies[i];
													  var eqPos = cookie.indexOf("=");
													  var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
													  document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT"; // 设置过期时间为过去时间
													}
													window.location.href = "/"
												}
										}); 
				
				});
				
				var lxkf = document.getElementById('lxkf');
				var fx = document.getElementById('fx');
				lxkf.addEventListener('click', function() {
								showModal("ts3");
							});
				fx.addEventListener('click', function() {
								showModal("ts4");
				});
					var lxkfff = document.getElementById('lxkfff');
			lxkfff.addEventListener('click', function() {
								var bts = document.getElementById("bts");
								bts.innerHTML = '开通会员请联系客服'
								gb(); // 先关闭当前对话框
								showModal("ts3"); // 然后显示新对话框
			});

			// 续费按钮事件监听器
			var renewVipBtn = document.getElementById('renewVipBtn');
			if (renewVipBtn) {
				renewVipBtn.addEventListener('click', function() {
					console.log('点击续费按钮');
					// 显示支付页面对话框
					showModal("ts7");
				});
			}
			}else{
			    var lxkff = document.getElementById('lxkff');
			lxkff.addEventListener('click', function() {
								showModal("ts3");
			});

			}
			
			function getCookie(cookieName) {
			  const strCookie = document.cookie
			  const cookieList = strCookie.split(';')
			  
			  for(let i = 0; i < cookieList.length; i++) {
			    const arr = cookieList[i].split('=')
			    if (cookieName === arr[0].trim()) {
			      return arr[1]
			    }
			  }
			  
			  return ''
			}

			
			var isCommitted = false;
			var isCommitted2 = false;
			var isCommitted3 = false;
			var isCommitted5 = false;
			
			document.addEventListener('DOMContentLoaded', function() {
                    document.querySelectorAll('input').forEach(function(input) {
                        input.addEventListener('click', function() {
                          tjnr = this.value
                        });
                    });
                    });
			
			function dosubmit5(){
			   if(isCommitted5==false){
				    if(!myForm5.xcxid.value&&myForm5.xcxid.value.length>18){
				        swal("请输入正确的id",'error');
				        myForm5.xcxid.focus();
				        return false;
				    }else{
				    
				    
                        
                            
                            console.log(tjnr)
                            if(tjnr=='绑定'){
                            var jz = document.getElementById("jz");
						    jz.style.display = "";
						    var arr = {lx:'xcx',xcxid:myForm5.xcxid.value,sj:sj()}
						    var form = document.getElementById("myForm5");
						    var input = document.createElement("input");
						    input.type = "hidden";
						    input.name = "data";
						    input.value = "value";
						    form.appendChild(input);
						    myForm5.data.value = jm(JSON.stringify(arr));
						    isCommitted5 = true;
						    return true;//返回true让表单正常提交
                            }else if(tjnr=='换绑'){
                            
                                var jz = document.getElementById("jz");
						    jz.style.display = "";
						    var arr = {lx:'xcxhb',xcxid:myForm5.xcxid.value,sj:sj()}
						    var form = document.getElementById("myForm5");
						    var input = document.createElement("input");
						    input.type = "hidden";
						    input.name = "data";
						    input.value = "value";
						    form.appendChild(input);
						    myForm5.data.value = jm(JSON.stringify(arr));
						    isCommitted5 = true;
						    return true;//返回true让表单正常提交
                            
                            }
                
                                
                                
                        
				    
						
					}
				}
			}
			
			function dosubmit3(){
			   if(isCommitted3==false){
				    if(!myForm3.ydrlz.value&&myForm3.ydrlz.value>0&&myForm3.ydrlz.value<99880){
				        swal("请输入正确的数量","1-99880 [建议5w左右]",'error');
				        myForm3.ydrlz.focus();
				        return false;
				    }else{
						var jz = document.getElementById("jz");
						jz.style.display = "";
						var arr = {lx:'ydkq',rlz:myForm3.ydrlz.value,sj:sj()}
						var form = document.getElementById("myForm3");
						var input = document.createElement("input");
						input.type = "hidden";input.name = "data";
						input.value = "value";
						form.appendChild(input);
						myForm3.data.value = jm(JSON.stringify(arr));
						isCommitted3 = true;
						return true;//返回true让表单正常提交
					}
				}
			}
			
			function dosubmit2(){
			   if(isCommitted2==false){
				    if(!myForm2.rlz.value&&myForm2.rlz.value>0&&myForm2.rlz.value<99880){
				        swal("请输入正确的数量","1-99880 [建议5w左右]",'error');
				        myForm2.rlz.focus();
				        return false;
				    }else{
						var jz = document.getElementById("jz");
						jz.style.display = "";
						var arr = {lx:'sbs',rlz:myForm2.rlz.value,sj:sj()}
						var form = document.getElementById("myForm2");
						var input = document.createElement("input");
						input.type = "hidden";input.name = "data";
						input.value = "value";
						form.appendChild(input);
						myForm2.data.value = jm(JSON.stringify(arr));
						isCommitted2 = true;
						return true;//返回true让表单正常提交
					}
				}
			}
			
			function dosubmit(){
			   if(isCommitted==false){
				    if(!myForm.user.value){
				        swal({title: "请输入账号",type: "error",confirmButtonText: "OK"});
				        myForm.user.focus();
				        return false;
				    }else if(!myForm.pass.value){
				        swal({title: "请输入密码",type: "error",confirmButtonText: "OK"});
				        myForm.pass.focus();
				        return false;
				    }else{
						var jz = document.getElementById("jz");
						jz.style.display = "";
						var arr = {lx:'dl',user:myForm.user.value,pass:myForm.pass.value,sj:sj()}
						var form = document.getElementById("myForm");var input = document.createElement("input");
						input.type = "hidden";input.name = "data";input.value = "value";form.appendChild(input);
						myForm.data.value = jm(JSON.stringify(arr));
						isCommitted = true;
						return true;//返回true让表单正常提交
					}
				}
			}
			
			function jm(data){
				var key  = CryptoJS.enc.Utf8.parse('apolkj*2$*3!~9s3');
				var iv   = CryptoJS.enc.Utf8.parse('eres12120ws+3s10');
				var encrypted = CryptoJS.AES.encrypt(data, key, {
				    iv: iv,
				    mode: CryptoJS.mode.CBC,
				    padding: CryptoJS.pad.Pkcs7
				});
				return encrypted.toString();
			}
			
			function sj(){
				var currentTime = new Date();  // 创建Date对象
				var hours = currentTime.getHours() < 10 ? "0" + currentTime.getHours() : currentTime.getHours();  // 获取小时
				var minutes = currentTime.getMinutes() < 10 ? "0" + currentTime.getMinutes() : currentTime.getMinutes();  // 获取分钟
				var seconds = currentTime.getSeconds()+10;
				if(seconds>=60){
					var minutes = minutes+1 < 10 ? "0" + minutes+1 : minutes+1;
					var seconds = seconds = '00'
				}
				if(minutes>60){
					var hours = hours+1 < 10 ? "0" + hours+1 : hours+1
					var minutes = minutes = '00'
				}
				if(hours>=24){
					var hours = hours = '00'
				}
				return hours+':'+minutes+':'+seconds
			}
			
		</script>
		
	</body>
</html>
<?php




echo '<script type="text/javascript">var jz = document.getElementById("jz");jz.style.display = "none";</script>';






?>