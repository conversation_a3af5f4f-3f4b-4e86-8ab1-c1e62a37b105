<?php
// 支付处理文件
header('Content-Type: application/json; charset=utf-8');

// 关闭错误显示（生产环境）
error_reporting(0);
ini_set('display_errors', 0);

// 引入易支付配置
$config = include 'epay_config.php';

// 引入数据库连接配置
$servername = "localhost";
$username = "fbsbs";
$password = "fbsbsxcx...";
$dbname = "fbsbs";

// 创建连接
$conn = new mysqli($servername, $username, $password, $dbname);
$conn->set_charset("utf8");

// 检查连接
if ($conn->connect_error) {
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

// 加密密钥（与主文件保持一致）
$key = 'apolkj*2$*3!~9s3';
$iv = 'eres12120ws+3s10';

/**
 * 生成签名
 * @param array $params 需要签名的参数数组
 * @param string $key 商户密钥
 * @return string 签名结果
 */
function getSign($params, $key) {
    // 1. 过滤空值和签名参数
    $params = array_filter($params, function($value, $k) {
        return $value !== '' && $value !== null && $k != 'sign' && $k != 'sign_type';
    }, ARRAY_FILTER_USE_BOTH);

    // 2. 按照参数名ASCII码从小到大排序
    ksort($params);

    // 3. 拼接成URL键值对
    $stringA = '';
    foreach ($params as $k => $v) {
        $stringA .= "{$k}={$v}&";
    }
    $stringA = rtrim($stringA, '&');

    // 4. 拼接商户密钥并进行MD5加密
    $stringSignTemp = $stringA . $key;
    return md5($stringSignTemp);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '请求方法错误']);
    exit;
}

// 获取POST数据
$package = $_POST['package'] ?? '';
$price = $_POST['price'] ?? '';
$payType = $_POST['pay_type'] ?? 'alipay'; // 默认支付宝

// 验证参数
if (empty($package) || empty($price)) {
    echo json_encode(['success' => false, 'message' => '参数错误']);
    exit;
}

// 验证支付方式
if (!in_array($payType, ['alipay', 'wxpay'])) {
    echo json_encode(['success' => false, 'message' => '不支持的支付方式']);
    exit;
}

// 验证用户登录状态
if (empty($_COOKIE['user'])) {
    echo json_encode(['success' => false, 'message' => '用户未登录', 'debug' => 'no_cookie', 'cookies' => array_keys($_COOKIE)]);
    exit;
}

try {
    // 解密用户信息
    $dat = openssl_decrypt($_COOKIE['user'], 'AES-128-CBC', $key, 0, $iv);
    $dat = json_decode($dat, true);

    if (!$dat || empty($dat['user'])) {
        echo json_encode(['success' => false, 'message' => '用户信息无效', 'debug' => 'decrypt_failed']);
        exit;
    }

    $user = $dat['user'];

    // 验证用户是否存在
    $stmt = $conn->prepare("SELECT * FROM user WHERE user = ?");
    $stmt->bind_param("s", $user);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => '用户不存在，请重新登录', 'debug' => 'user_not_found', 'username' => $user]);
        exit;
    }

    // 生成商户订单号
    $out_trade_no = date('YmdHis') . rand(100000, 999999);

    // 构建支付参数
    $params = [
        'pid' => $config['pid'],
        'type' => $payType,
        'out_trade_no' => $out_trade_no,
        'notify_url' => $config['notify_url'],
        'return_url' => $config['return_url'],
        'name' => '会员套餐',
        'money' => $price,
        'clientip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
        'device' => 'pc',
        'sitename' => $config['sitename'],
        'param' => json_encode(['user' => $user, 'package' => $package]) // 传递用户和套餐信息
    ];

    // 生成签名
    $sign = getSign($params, $config['key']);
    $params['sign'] = $sign;
    $params['sign_type'] = 'MD5';

    // 发送请求到易支付API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $config['api_url']);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception('CURL错误: ' . $error);
    }

    if ($httpCode !== 200) {
        throw new Exception('HTTP错误: ' . $httpCode);
    }

    $result = json_decode($response, true);

    if (!$result) {
        throw new Exception('API返回数据格式错误: ' . $response);
    }

    if ($result['code'] == 1) {
        // 记录API返回的原始数据
        error_log("易支付API返回数据: " . json_encode($result));

        // 支付成功，返回标准格式
        $successResponse = [
            'success' => true,
            'message' => '支付订单创建成功',
            'trade_no' => $result['trade_no'] ?? $out_trade_no,
            'money' => $result['money'] ?? $price,
            'package' => $package,
            'price' => $price,
            'pay_type' => $payType,
            'pay_method' => 'qrcode'
        ];

        // 根据返回的字段设置支付链接
        if (isset($result['qrcode'])) {
            $successResponse['qrcode'] = $result['qrcode'];
            error_log("使用qrcode字段: " . $result['qrcode']);
        } elseif (isset($result['payurl'])) {
            $successResponse['qrcode'] = $result['payurl'];
            $successResponse['pay_method'] = 'redirect';
            error_log("使用payurl字段: " . $result['payurl']);
        } elseif (isset($result['urlscheme'])) {
            $successResponse['qrcode'] = $result['urlscheme'];
            $successResponse['pay_method'] = 'scheme';
            error_log("使用urlscheme字段: " . $result['urlscheme']);
        } else {
            error_log("API返回数据中没有找到支付链接字段");
            throw new Exception('API未返回支付链接');
        }

        error_log("最终响应数据: " . json_encode($successResponse));
        echo json_encode($successResponse);
    } else {
        throw new Exception($result['msg'] ?? '支付接口返回错误');
    }
    
} catch (Exception $e) {
    // 错误处理
    $errorResponse = [
        'success' => false,
        'message' => '支付订单创建失败: ' . $e->getMessage()
    ];

    echo json_encode($errorResponse);
}

$conn->close();
?>
