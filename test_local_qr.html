<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地二维码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .qr-container {
            margin: 15px 0;
            min-height: 220px;
            border: 1px dashed #ccc;
            padding: 10px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>本地二维码库测试</h1>
        
        <div class="test-section">
            <h3>测试1: HTML版本二维码</h3>
            <div class="qr-container" id="qr-html"></div>
            <button onclick="testHTMLQR()">生成HTML二维码</button>
            <div id="html-status"></div>
        </div>

        <div class="test-section">
            <h3>测试2: Canvas版本二维码</h3>
            <div class="qr-container" id="qr-canvas"></div>
            <button onclick="testCanvasQR()">生成Canvas二维码</button>
            <div id="canvas-status"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 支付界面模拟</h3>
            <div class="qr-container" id="qr-payment"></div>
            <button onclick="testPaymentQR()">模拟支付二维码</button>
            <div id="payment-status"></div>
        </div>
    </div>

    <!-- 本地二维码库 -->
    <script src="qrcode_local.js"></script>
    <script>
        // 测试用的支付链接
        const testQRData = 'https://qr.alipay.com/tsx12473ducxlzawpdayn0b';
        
        // 测试HTML版本
        function testHTMLQR() {
            const container = document.getElementById('qr-html');
            const status = document.getElementById('html-status');
            
            container.innerHTML = '';
            status.innerHTML = '正在生成...';
            
            try {
                var qr = new QRCode(container, {
                    text: testQRData,
                    width: 200,
                    height: 200,
                    colorDark: "#000000",
                    colorLight: "#ffffff"
                });
                
                status.innerHTML = '<span class="success">✓ HTML二维码生成成功</span>';
                console.log('HTML二维码生成成功');
            } catch (error) {
                status.innerHTML = '<span class="error">✗ HTML二维码生成失败: ' + error.message + '</span>';
                console.error('HTML二维码生成失败:', error);
            }
        }

        // 测试Canvas版本
        function testCanvasQR() {
            const container = document.getElementById('qr-canvas');
            const status = document.getElementById('canvas-status');
            
            container.innerHTML = '';
            status.innerHTML = '正在生成...';
            
            try {
                var canvas = document.createElement('canvas');
                canvas.style.border = '1px solid #ddd';
                canvas.style.borderRadius = '8px';
                container.appendChild(canvas);
                
                QRCode.toCanvas(canvas, testQRData, {
                    width: 200,
                    height: 200,
                    color: {
                        dark: "#000000",
                        light: "#ffffff"
                    }
                }, function(error) {
                    if (error) {
                        status.innerHTML = '<span class="error">✗ Canvas二维码生成失败: ' + error.message + '</span>';
                        console.error('Canvas二维码生成失败:', error);
                    } else {
                        status.innerHTML = '<span class="success">✓ Canvas二维码生成成功</span>';
                        console.log('Canvas二维码生成成功');
                    }
                });
            } catch (error) {
                status.innerHTML = '<span class="error">✗ Canvas二维码生成异常: ' + error.message + '</span>';
                console.error('Canvas二维码生成异常:', error);
            }
        }

        // 测试支付界面
        function testPaymentQR() {
            const container = document.getElementById('qr-payment');
            const status = document.getElementById('payment-status');
            
            container.innerHTML = '';
            status.innerHTML = '正在生成支付二维码...';
            
            // 模拟支付界面的二维码生成逻辑
            try {
                // 首先尝试Canvas版本
                var canvas = document.createElement('canvas');
                canvas.style.cssText = `
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    background: white;
                `;
                container.appendChild(canvas);
                
                QRCode.toCanvas(canvas, testQRData, {
                    width: 200,
                    height: 200,
                    color: {
                        dark: "#000000",
                        light: "#ffffff"
                    }
                }, function(error) {
                    if (error) {
                        console.log('Canvas失败，尝试HTML版本');
                        container.innerHTML = '';
                        
                        // 如果Canvas失败，使用HTML版本
                        var qr = new QRCode(container, {
                            text: testQRData,
                            width: 200,
                            height: 200,
                            colorDark: "#000000",
                            colorLight: "#ffffff"
                        });
                        
                        status.innerHTML = '<span class="success">✓ 支付二维码生成成功 (HTML版本)</span>';
                    } else {
                        status.innerHTML = '<span class="success">✓ 支付二维码生成成功 (Canvas版本)</span>';
                    }
                });
            } catch (error) {
                status.innerHTML = '<span class="error">✗ 支付二维码生成失败: ' + error.message + '</span>';
                console.error('支付二维码生成失败:', error);
            }
        }

        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，QRCode库状态:', typeof QRCode);
            
            // 自动运行第一个测试
            setTimeout(function() {
                testHTMLQR();
            }, 500);
        });
    </script>
</body>
</html>
