<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付界面测试</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js" onerror="loadBackupQRCode()"></script>
    <script>
        function loadBackupQRCode() {
            console.log('主要二维码库加载失败，尝试备用CDN');
            var script = document.createElement('script');
            script.src = 'https://unpkg.com/qrcode@1.5.3/build/qrcode.min.js';
            script.onerror = function() {
                console.log('备用二维码库也加载失败，将使用在线服务');
                window.QRCodeLoadFailed = true;
            };
            document.head.appendChild(script);
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-btn {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .alipay-btn {
            background: #1677FF;
            color: white;
        }
        .alipay-btn:hover {
            background: #0958d9;
        }
        .wxpay-btn {
            background: #07C160;
            color: white;
        }
        .wxpay-btn:hover {
            background: #06AD56;
        }
        .device-info {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>支付界面测试</h2>
        
        <div class="device-info">
            <strong>当前设备：</strong><span id="deviceType"></span><br>
            <strong>用户代理：</strong><span id="userAgent"></span>
        </div>

        <button class="test-btn alipay-btn" onclick="testPayment('alipay')">
            测试支付宝支付
        </button>
        
        <button class="test-btn wxpay-btn" onclick="testPayment('wxpay')">
            测试微信支付
        </button>

        <h3>测试说明</h3>
        <ul>
            <li><strong>PC端：</strong>应该显示二维码模态框</li>
            <li><strong>移动端：</strong>应该直接跳转到支付页面</li>
            <li>支付完成后会自动检测状态并刷新页面</li>
        </ul>
    </div>

    <script>
        // 设备检测函数
        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        // 获取支付方式名称
        function getPaymentTypeName(payType) {
            return payType === 'wxpay' ? '微信支付' : '支付宝支付';
        }

        // 移动端支付处理
        function handleMobilePayment(paymentUrl, payType) {
            console.log('移动端支付处理:', paymentUrl);
            
            swal({
                title: "正在跳转到" + getPaymentTypeName(payType),
                text: "请在打开的页面中完成支付",
                type: "info",
                timer: 2000,
                showConfirmButton: false
            });

            setTimeout(function() {
                window.location.href = paymentUrl;
            }, 1000);
        }

        // 显示二维码模态框
        function showQRCodeModal(qrcode, tradeNo, amount, payType) {
            console.log('显示二维码模态框:', qrcode);

            var modal = document.createElement('div');
            modal.id = 'qrcodeModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                box-sizing: border-box;
            `;

            var modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 15px;
                padding: 30px;
                text-align: center;
                max-width: 400px;
                width: 100%;
                position: relative;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            `;

            var closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 15px;
                right: 20px;
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
            `;

            var title = document.createElement('h3');
            title.textContent = getPaymentTypeName(payType) + ' - 扫码支付';
            title.style.margin = '0 0 20px 0';

            var qrcodeContainer = document.createElement('div');
            qrcodeContainer.style.margin = '20px 0';

            var amountDiv = document.createElement('div');
            amountDiv.textContent = '支付金额：￥' + amount;
            amountDiv.style.cssText = 'font-size: 16px; font-weight: bold; margin: 15px 0;';

            var tipDiv = document.createElement('div');
            tipDiv.innerHTML = '请使用手机<strong>' + getPaymentTypeName(payType) + '</strong>扫描二维码完成支付';
            tipDiv.style.cssText = 'font-size: 14px; color: #666; margin: 15px 0;';

            modalContent.appendChild(closeBtn);
            modalContent.appendChild(title);
            modalContent.appendChild(amountDiv);
            modalContent.appendChild(qrcodeContainer);
            modalContent.appendChild(tipDiv);
            modal.appendChild(modalContent);

            document.body.appendChild(modal);

            // 生成二维码
            console.log('QRCode库状态:', typeof QRCode);
            console.log('QRCodeLoadFailed状态:', window.QRCodeLoadFailed);

            if (typeof QRCode !== 'undefined' && !window.QRCodeLoadFailed) {
                try {
                    var canvas = document.createElement('canvas');
                    canvas.style.border = '1px solid #ddd';
                    canvas.style.borderRadius = '8px';
                    qrcodeContainer.appendChild(canvas);

                    QRCode.toCanvas(canvas, qrcode, {
                        width: 200,
                        height: 200,
                        margin: 2,
                        color: {
                            dark: "#000000",
                            light: "#ffffff"
                        }
                    }, function (error) {
                        if (error) {
                            console.error('二维码生成失败:', error);
                            showFallbackQR(qrcodeContainer, qrcode);
                        } else {
                            console.log('二维码生成成功');
                        }
                    });
                } catch (e) {
                    console.error('二维码生成异常:', e);
                    showFallbackQR(qrcodeContainer, qrcode);
                }
            } else {
                console.warn('QRCode库不可用，使用备用方案');
                showFallbackQR(qrcodeContainer, qrcode);
            }

            // 备用二维码显示方案
            function showFallbackQR(container, qrcode) {
                container.innerHTML = '';

                // 使用在线二维码生成服务
                var qrImg = document.createElement('img');
                qrImg.style.cssText = `
                    width: 200px;
                    height: 200px;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                `;

                var qrServices = [
                    'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(qrcode),
                    'https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=' + encodeURIComponent(qrcode)
                ];

                var serviceIndex = 0;

                function tryNextService() {
                    if (serviceIndex < qrServices.length) {
                        qrImg.src = qrServices[serviceIndex];
                        serviceIndex++;
                    } else {
                        // 所有服务都失败，显示链接
                        container.innerHTML = '<p style="color: red;">二维码生成失败</p><a href="' + qrcode + '" target="_blank" style="color: blue;">点击此处支付</a>';
                    }
                }

                qrImg.onload = function() {
                    console.log('在线二维码生成成功');
                    container.appendChild(qrImg);
                };

                qrImg.onerror = function() {
                    console.log('在线二维码服务失败，尝试下一个');
                    tryNextService();
                };

                tryNextService();
            }

            closeBtn.addEventListener('click', function() {
                modal.remove();
            });

            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 测试支付函数
        function testPayment(payType) {
            var isMobile = isMobileDevice();
            console.log('测试支付:', payType, '设备类型:', isMobile ? '移动端' : 'PC端');

            // 模拟支付数据
            var mockResponse = {
                success: true,
                trade_no: 'TEST' + Date.now(),
                money: '0.01',
                pay_type: payType,
                pay_method: 'qrcode',
                qrcode: payType === 'alipay' ? 
                    'https://qr.alipay.com/tsx12473ducxlzawpdayn0b' : 
                    'weixin://wxpay/bizpayurl?pr=04IPMKM'
            };

            if (isMobile) {
                handleMobilePayment(mockResponse.qrcode, payType);
            } else {
                showQRCodeModal(mockResponse.qrcode, mockResponse.trade_no, mockResponse.money, payType);
            }
        }

        // 页面加载时显示设备信息
        document.addEventListener('DOMContentLoaded', function() {
            var isMobile = isMobileDevice();
            document.getElementById('deviceType').textContent = isMobile ? '移动端' : 'PC端';
            document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 50) + '...';
        });
    </script>
</body>
</html>
