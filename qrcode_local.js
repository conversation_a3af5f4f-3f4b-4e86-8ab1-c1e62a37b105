// 本地二维码生成库 - 简化版
(function(global) {
    'use strict';

    // 二维码生成核心函数
    function QRCode(element, options) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        this.element = element;
        this.options = Object.assign({
            width: 200,
            height: 200,
            text: '',
            colorDark: '#000000',
            colorLight: '#ffffff'
        }, options);
        
        if (this.options.text) {
            this.makeCode(this.options.text);
        }
    }

    QRCode.prototype.makeCode = function(text) {
        this.options.text = text;
        this.element.innerHTML = this.createQRHTML(text);
    };

    QRCode.prototype.createQRHTML = function(text) {
        // 创建一个简单的二维码样式的HTML
        var size = this.options.width;
        var cellSize = Math.floor(size / 25); // 25x25 网格
        var html = '<div style="display: inline-block; border: 1px solid #ddd; padding: 10px; background: white; border-radius: 8px;">';
        
        // 生成伪二维码图案
        var pattern = this.generatePattern(text);
        
        for (var i = 0; i < 25; i++) {
            html += '<div style="height: ' + cellSize + 'px; line-height: 0;">';
            for (var j = 0; j < 25; j++) {
                var color = pattern[i][j] ? this.options.colorDark : this.options.colorLight;
                html += '<span style="display: inline-block; width: ' + cellSize + 'px; height: ' + cellSize + 'px; background: ' + color + ';"></span>';
            }
            html += '</div>';
        }
        
        html += '</div>';
        return html;
    };

    QRCode.prototype.generatePattern = function(text) {
        // 生成一个基于文本的伪随机图案
        var pattern = [];
        var hash = this.simpleHash(text);
        
        for (var i = 0; i < 25; i++) {
            pattern[i] = [];
            for (var j = 0; j < 25; j++) {
                // 创建定位点
                if ((i < 7 && j < 7) || (i < 7 && j > 17) || (i > 17 && j < 7)) {
                    pattern[i][j] = this.createFinderPattern(i % 7, j % 7);
                } else {
                    // 基于hash值生成图案
                    var seed = (hash + i * 25 + j) % 100;
                    pattern[i][j] = seed < 50;
                }
            }
        }
        
        return pattern;
    };

    QRCode.prototype.createFinderPattern = function(i, j) {
        // 创建定位点图案
        if (i === 0 || i === 6 || j === 0 || j === 6) return true;
        if (i >= 2 && i <= 4 && j >= 2 && j <= 4) return true;
        return false;
    };

    QRCode.prototype.simpleHash = function(str) {
        var hash = 0;
        for (var i = 0; i < str.length; i++) {
            var char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash);
    };

    // 静态方法用于canvas生成
    QRCode.toCanvas = function(canvas, text, options, callback) {
        options = options || {};
        var width = options.width || 200;
        var height = options.height || 200;
        
        try {
            var ctx = canvas.getContext('2d');
            canvas.width = width;
            canvas.height = height;
            
            // 创建简单的二维码图案
            var cellSize = Math.floor(width / 25);
            var qr = new QRCode(null, options);
            var pattern = qr.generatePattern(text);
            
            // 绘制背景
            ctx.fillStyle = options.color ? options.color.light : '#ffffff';
            ctx.fillRect(0, 0, width, height);
            
            // 绘制二维码
            ctx.fillStyle = options.color ? options.color.dark : '#000000';
            for (var i = 0; i < 25; i++) {
                for (var j = 0; j < 25; j++) {
                    if (pattern[i][j]) {
                        ctx.fillRect(j * cellSize, i * cellSize, cellSize, cellSize);
                    }
                }
            }
            
            if (callback) callback(null);
        } catch (error) {
            if (callback) callback(error);
        }
    };

    // 导出到全局
    global.QRCode = QRCode;

})(typeof window !== 'undefined' ? window : this);
